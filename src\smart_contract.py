"""
Gestion des smart contracts pour les commandes de matières premières
"""
import json
import hashlib
import logging
from datetime import datetime
from typing import Dict, Optional, List
from enum import Enum
from config.settings import Config

class ContractStatus(Enum):
    """États possibles d'un smart contract"""
    DRAFT = "draft"
    PENDING_PRICE = "pending_price"
    READY_TO_EXECUTE = "ready_to_execute"
    EXECUTED = "executed"
    CANCELLED = "cancelled"

class SmartContract:
    """Représente un smart contract pour une commande de matière première"""
    
    def __init__(self, material_name: str, quantity_to_order: int, unit: str, 
                 supplier: str, provisional_price: float = 0.0):
        self.contract_id = self._generate_contract_id()
        self.material_name = material_name
        self.quantity_to_order = quantity_to_order
        self.unit = unit
        self.supplier = supplier
        self.provisional_price = provisional_price
        self.confirmed_price: Optional[float] = None
        self.status = ContractStatus.DRAFT
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.price_confirmation_deadline: Optional[datetime] = None
        self.execution_hash: Optional[str] = None
        self.metadata = {}
    
    def _generate_contract_id(self) -> str:
        """Génère un ID unique pour le contrat"""
        timestamp = str(datetime.now().timestamp())
        random_data = f"{timestamp}_{self.material_name if hasattr(self, 'material_name') else 'unknown'}"
        return hashlib.sha256(random_data.encode()).hexdigest()[:16].upper()
    
    def set_provisional_price(self, price: float):
        """Définit le prix provisoire et passe en attente de confirmation"""
        self.provisional_price = price
        self.status = ContractStatus.PENDING_PRICE
        self.updated_at = datetime.now()
        
        # Deadline de 24h pour la confirmation du prix
        from datetime import timedelta
        self.price_confirmation_deadline = datetime.now() + timedelta(hours=24)
    
    def confirm_price(self, confirmed_price: float) -> bool:
        """Confirme le prix final et prépare le contrat pour exécution"""
        if self.status != ContractStatus.PENDING_PRICE:
            return False
        
        self.confirmed_price = confirmed_price
        self.status = ContractStatus.READY_TO_EXECUTE
        self.updated_at = datetime.now()
        return True
    
    def execute_contract(self) -> str:
        """Exécute le contrat et génère un hash de transaction"""
        if self.status != ContractStatus.READY_TO_EXECUTE:
            raise ValueError(f"Contrat non prêt pour exécution. Statut actuel: {self.status.value}")
        
        if self.confirmed_price is None:
            raise ValueError("Prix non confirmé")
        
        # Génération du hash d'exécution
        execution_data = {
            "contract_id": self.contract_id,
            "material_name": self.material_name,
            "quantity": self.quantity_to_order,
            "unit": self.unit,
            "supplier": self.supplier,
            "price": self.confirmed_price,
            "total_amount": self.quantity_to_order * self.confirmed_price,
            "execution_timestamp": datetime.now().isoformat()
        }
        
        execution_string = json.dumps(execution_data, sort_keys=True)
        self.execution_hash = hashlib.sha256(execution_string.encode()).hexdigest()
        
        self.status = ContractStatus.EXECUTED
        self.updated_at = datetime.now()
        
        return self.execution_hash
    
    def cancel_contract(self, reason: str = ""):
        """Annule le contrat"""
        self.status = ContractStatus.CANCELLED
        self.updated_at = datetime.now()
        self.metadata["cancellation_reason"] = reason
    
    def get_total_amount(self) -> float:
        """Calcule le montant total de la commande"""
        price = self.confirmed_price if self.confirmed_price else self.provisional_price
        return self.quantity_to_order * price
    
    def is_expired(self) -> bool:
        """Vérifie si le contrat a expiré"""
        if self.price_confirmation_deadline is None:
            return False
        return datetime.now() > self.price_confirmation_deadline
    
    def to_dict(self) -> Dict:
        """Convertit le contrat en dictionnaire"""
        return {
            "contract_id": self.contract_id,
            "material_name": self.material_name,
            "quantity_to_order": self.quantity_to_order,
            "unit": self.unit,
            "supplier": self.supplier,
            "provisional_price": self.provisional_price,
            "confirmed_price": self.confirmed_price,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "price_confirmation_deadline": self.price_confirmation_deadline.isoformat() if self.price_confirmation_deadline else None,
            "execution_hash": self.execution_hash,
            "total_amount": self.get_total_amount(),
            "is_expired": self.is_expired(),
            "metadata": self.metadata
        }
    
    def to_json(self) -> str:
        """Convertit le contrat en JSON"""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)

class SmartContractManager:
    """Gestionnaire des smart contracts"""
    
    def __init__(self):
        self.contracts: Dict[str, SmartContract] = {}
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """Configure le système de logging"""
        logger = logging.getLogger('SmartContractManager')
        logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def create_contract(self, material_name: str, quantity_to_order: int, 
                       unit: str, supplier: str, provisional_price: float = 0.0) -> SmartContract:
        """Crée un nouveau smart contract"""
        contract = SmartContract(
            material_name, quantity_to_order, unit, supplier, provisional_price
        )
        
        self.contracts[contract.contract_id] = contract
        
        self.logger.info(
            f"Nouveau contrat créé - ID: {contract.contract_id}, "
            f"Matière: {material_name}, Quantité: {quantity_to_order} {unit}"
        )
        
        return contract
    
    def get_contract(self, contract_id: str) -> Optional[SmartContract]:
        """Récupère un contrat par son ID"""
        return self.contracts.get(contract_id)
    
    def get_contracts_by_status(self, status: ContractStatus) -> List[SmartContract]:
        """Récupère tous les contrats ayant un statut donné"""
        return [contract for contract in self.contracts.values() if contract.status == status]
    
    def get_pending_contracts(self) -> List[SmartContract]:
        """Récupère tous les contrats en attente de confirmation de prix"""
        return self.get_contracts_by_status(ContractStatus.PENDING_PRICE)
    
    def get_ready_contracts(self) -> List[SmartContract]:
        """Récupère tous les contrats prêts à être exécutés"""
        return self.get_contracts_by_status(ContractStatus.READY_TO_EXECUTE)
    
    def cleanup_expired_contracts(self) -> int:
        """Nettoie les contrats expirés et retourne le nombre de contrats annulés"""
        expired_count = 0
        
        for contract in self.contracts.values():
            if contract.is_expired() and contract.status == ContractStatus.PENDING_PRICE:
                contract.cancel_contract("Délai de confirmation du prix expiré")
                expired_count += 1
                self.logger.warning(f"Contrat expiré annulé: {contract.contract_id}")
        
        return expired_count
    
    def export_contracts(self, filename: Optional[str] = None) -> str:
        """Exporte tous les contrats en JSON"""
        if filename is None:
            filename = f"logs/contracts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "total_contracts": len(self.contracts),
            "contracts": [contract.to_dict() for contract in self.contracts.values()]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Contrats exportés vers: {filename}")
        return filename
    
    def get_statistics(self) -> Dict:
        """Retourne des statistiques sur les contrats"""
        total = len(self.contracts)
        by_status = {}
        
        for status in ContractStatus:
            count = len(self.get_contracts_by_status(status))
            by_status[status.value] = count
        
        total_value = sum(contract.get_total_amount() for contract in self.contracts.values())
        
        return {
            "total_contracts": total,
            "contracts_by_status": by_status,
            "total_value": total_value,
            "average_value": total_value / total if total > 0 else 0
        }

if __name__ == "__main__":
    # Test du gestionnaire de contrats
    manager = SmartContractManager()
    
    print("=== Test du Gestionnaire de Smart Contracts ===")
    
    # Création d'un contrat de test
    contract = manager.create_contract(
        material_name="Acier inoxydable",
        quantity_to_order=50,
        unit="kg",
        supplier="MetalCorp",
        provisional_price=15.50
    )
    
    print(f"Contrat créé: {contract.contract_id}")
    print(f"Statut: {contract.status.value}")
    
    # Test de confirmation de prix
    contract.set_provisional_price(15.50)
    print(f"Prix provisoire défini, nouveau statut: {contract.status.value}")
    
    # Simulation de confirmation
    contract.confirm_price(16.20)
    print(f"Prix confirmé, nouveau statut: {contract.status.value}")
    
    # Exécution du contrat
    execution_hash = contract.execute_contract()
    print(f"Contrat exécuté, hash: {execution_hash}")
    
    # Statistiques
    stats = manager.get_statistics()
    print(f"\nStatistiques: {stats}")
