"""
Simulateur de lecteur RFID pour le suivi des stocks de matières premières
"""
import random
import time
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional
from config.settings import Config, MATERIALS

class RFIDTag:
    """Représente un tag RFID attaché à une matière première"""
    
    def __init__(self, material_name: str, initial_quantity: int, unit: str, supplier: str):
        self.tag_id = self._generate_tag_id()
        self.material_name = material_name
        self.current_quantity = initial_quantity
        self.unit = unit
        self.supplier = supplier
        self.last_updated = datetime.now()
        self.threshold_alerts = 0
    
    def _generate_tag_id(self) -> str:
        """Génère un ID unique pour le tag RFID"""
        return f"RFID_{random.randint(100000, 999999)}"
    
    def update_quantity(self, new_quantity: int) -> bool:
        """Met à jour la quantité et retourne True si seuil critique atteint"""
        self.current_quantity = new_quantity
        self.last_updated = datetime.now()
        
        if new_quantity < Config.STOCK_THRESHOLD:
            self.threshold_alerts += 1
            return True
        return False
    
    def to_dict(self) -> Dict:
        """Convertit le tag en dictionnaire pour logging"""
        return {
            "tag_id": self.tag_id,
            "material_name": self.material_name,
            "current_quantity": self.current_quantity,
            "unit": self.unit,
            "supplier": self.supplier,
            "last_updated": self.last_updated.isoformat(),
            "threshold_alerts": self.threshold_alerts,
            "is_critical": self.current_quantity < Config.STOCK_THRESHOLD
        }

class RFIDSimulator:
    """Simulateur de lecteur RFID pour la gestion des stocks"""
    
    def __init__(self):
        self.tags: List[RFIDTag] = []
        self.logger = self._setup_logger()
        self.critical_events: List[Dict] = []
        self._initialize_tags()
    
    def _setup_logger(self) -> logging.Logger:
        """Configure le système de logging"""
        logger = logging.getLogger('RFIDSimulator')
        logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # Handler pour fichier
        file_handler = logging.FileHandler(Config.LOG_FILE)
        file_handler.setLevel(logging.INFO)
        
        # Handler pour console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Format des logs
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _initialize_tags(self):
        """Initialise les tags RFID avec des quantités aléatoires"""
        for material in MATERIALS:
            initial_quantity = random.randint(
                Config.INITIAL_STOCK_MIN, 
                Config.INITIAL_STOCK_MAX
            )
            
            tag = RFIDTag(
                material["name"],
                initial_quantity,
                material["unit"],
                material["supplier"]
            )
            
            self.tags.append(tag)
            self.logger.info(f"Tag RFID initialisé: {tag.material_name} - {initial_quantity} {tag.unit}")
    
    def simulate_stock_consumption(self) -> List[Dict]:
        """Simule la consommation de stock et retourne les événements critiques"""
        critical_events = []
        
        for tag in self.tags:
            # Simulation de consommation aléatoire (1-8 unités)
            consumption = random.randint(1, 8)
            new_quantity = max(0, tag.current_quantity - consumption)
            
            # Vérification du seuil critique
            is_critical = tag.update_quantity(new_quantity)
            
            event_data = {
                "timestamp": datetime.now().isoformat(),
                "event_type": "stock_update",
                "tag_data": tag.to_dict(),
                "consumption": consumption,
                "is_critical": is_critical
            }
            
            self.logger.info(
                f"Stock mis à jour - {tag.material_name}: "
                f"{tag.current_quantity + consumption} -> {tag.current_quantity} {tag.unit}"
            )
            
            if is_critical:
                critical_events.append(event_data)
                self.critical_events.append(event_data)
                self.logger.warning(
                    f"SEUIL CRITIQUE ATTEINT - {tag.material_name}: "
                    f"{tag.current_quantity} {tag.unit} (seuil: {Config.STOCK_THRESHOLD})"
                )
        
        return critical_events
    
    def get_stock_status(self) -> Dict:
        """Retourne le statut complet des stocks"""
        total_items = len(self.tags)
        critical_items = sum(1 for tag in self.tags if tag.current_quantity < Config.STOCK_THRESHOLD)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "total_materials": total_items,
            "critical_materials": critical_items,
            "critical_percentage": (critical_items / total_items) * 100 if total_items > 0 else 0,
            "materials": [tag.to_dict() for tag in self.tags]
        }
    
    def get_critical_materials(self) -> List[RFIDTag]:
        """Retourne la liste des matières premières en situation critique"""
        return [tag for tag in self.tags if tag.current_quantity < Config.STOCK_THRESHOLD]
    
    def export_logs(self, filename: Optional[str] = None) -> str:
        """Exporte les logs en format JSON"""
        if filename is None:
            filename = f"logs/rfid_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "stock_status": self.get_stock_status(),
            "critical_events": self.critical_events
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Logs exportés vers: {filename}")
        return filename
    
    def run_simulation(self, duration_minutes: int = 1):
        """Lance la simulation pour une durée donnée"""
        self.logger.info(f"Démarrage de la simulation RFID pour {duration_minutes} minute(s)")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        while time.time() < end_time:
            critical_events = self.simulate_stock_consumption()
            
            if critical_events:
                self.logger.info(f"{len(critical_events)} événement(s) critique(s) détecté(s)")
            
            time.sleep(Config.RFID_SIMULATION_INTERVAL)
        
        self.logger.info("Simulation RFID terminée")
        return self.get_critical_materials()

if __name__ == "__main__":
    # Test du simulateur
    simulator = RFIDSimulator()
    print("=== Test du Simulateur RFID ===")
    print(f"Matières premières initialisées: {len(simulator.tags)}")
    
    # Simulation courte
    critical_materials = simulator.run_simulation(duration_minutes=0.5)
    
    print(f"\nMatières en situation critique: {len(critical_materials)}")
    for material in critical_materials:
        print(f"- {material.material_name}: {material.current_quantity} {material.unit}")
    
    # Export des logs
    export_file = simulator.export_logs()
    print(f"\nLogs exportés: {export_file}")
