# Guide d'Installation et d'Exécution

## Prérequis

- Python 3.8 ou supérieur
- pip (gestionnaire de paquets Python)
- Connexion Internet (pour l'installation des dépendances)

## Installation

### 1. Vérification de Python

```bash
python --version
# ou
python3 --version
```

### 2. Installation des dépendances

```bash
pip install -r requirements.txt
```

**Note:** Certaines dépendances dans requirements.txt sont des modules Python standard et n'ont pas besoin d'être installées séparément.

### 3. Configuration

Le fichier `.env` est déjà configuré avec des valeurs de démonstration. Aucune modification n'est nécessaire pour la démonstration.

## Exécution

### Option 1: Démonstration Interactive (Recommandée)

```bash
python demo.py
```

Cette option lance une démonstration guidée étape par étape qui explique chaque composant du système.

### Option 2: Exécution Automatique

```bash
python src/main.py
```

Cette option lance le système complet en mode automatique pour 2 minutes.

### Option 3: Tests Unitaires

```bash
python tests/test_system.py
```

Cette option exécute tous les tests unitaires pour valider le fonctionnement du système.

## Structure des Fichiers Générés

Après exécution, le système génère plusieurs fichiers de logs :

```
logs/
├── stock_management.log          # Log principal du système
├── rfid_export_YYYYMMDD_HHMMSS.json     # Export des données RFID
├── contracts_export_YYYYMMDD_HHMMSS.json # Export des contrats
├── email_logs_YYYYMMDD_HHMMSS.json      # Logs des emails
├── hedera_deployments_YYYYMMDD_HHMMSS.json # Déploiements Hashgraph
├── workflow_logs_YYYYMMDD_HHMMSS.json   # Logs du workflow
└── complete_report_YYYYMMDD_HHMMSS.json # Rapport complet
```

## Fonctionnalités Démontrées

✅ **Simulation RFID**: Génération dynamique de stocks et détection de seuils critiques  
✅ **Smart Contracts**: Création, gestion d'états et exécution automatique  
✅ **Communication Email**: Envoi automatisé de demandes de prix  
✅ **Hedera Hashgraph**: Déploiement simulé de contrats sur blockchain  
✅ **Flux Agentique**: Orchestration intelligente de tout le processus  
✅ **Logging Complet**: Traçabilité de toutes les opérations  

## Résolution de Problèmes

### Erreur: Module non trouvé

```bash
# Assurez-vous d'être dans le bon répertoire
cd blockchain_project

# Réinstallez les dépendances
pip install -r requirements.txt
```

### Erreur: Permission refusée

```bash
# Sur Linux/Mac, utilisez python3
python3 demo.py

# Ou donnez les permissions d'exécution
chmod +x demo.py
./demo.py
```

### Erreur: Variables d'environnement

Le fichier `.env` est déjà configuré avec des valeurs de test. Si vous souhaitez utiliser de vraies credentials Hedera :

1. Créez un compte sur [Hedera Portal](https://portal.hedera.com/)
2. Modifiez les valeurs dans `.env`
3. Remplacez `MockHederaClient` par le vrai SDK dans `hashgraph_client.py`

## Support

Pour toute question ou problème :

1. Vérifiez les logs dans le dossier `logs/`
2. Consultez le rapport technique `RAPPORT_TECHNIQUE.md`
3. Exécutez les tests unitaires pour identifier les problèmes

## Démonstration Vidéo

Pour une démonstration complète, exécutez :

```bash
python demo.py
```

Et suivez les instructions à l'écran. La démonstration dure environ 5-10 minutes et couvre tous les aspects du système.
