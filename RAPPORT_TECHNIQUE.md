# Rapport Technique - Suivi Intelligent des Stocks avec RFID et Hashgraph

**Auteur:** Pr <PERSON> SOUISSI  
**Date:** Juin 2024  
**Projet:** Mini-projet final "Blockchain et IoT"

## Résumé Exécutif

Ce projet implémente une solution IoT sécurisée basée sur la technologie Hedera Hashgraph et des smart contracts dynamiques, permettant de simuler la gestion automatisée des stocks de matières premières à l'aide de tags RFID. Le système intègre l'envoi d'emails automatisés pour la mise à jour des prix, la génération de smart contracts mis à jour, et un flux agentique garantissant la réactivité du système.

## Architecture Technique

### 1. Vue d'ensemble du système

Le système est composé de 5 modules principaux interconnectés :

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  RFID Simulator │───▶│ Smart Contract   │───▶│ Email Handler   │
│                 │    │ Manager          │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       ▼
         │              ┌──────────────────┐    ┌─────────────────┐
         └─────────────▶│ Agentic Workflow │◀───│ Hashgraph Client│
                        │                  │    │                 │
                        └──────────────────┘    └─────────────────┘
```

### 2. Modules détaillés

#### 2.1 RFID Simulator (`rfid_simulator.py`)
- **Fonction:** Simulation de lecteurs RFID pour le suivi des stocks
- **Caractéristiques:**
  - Génération dynamique de stocks simulés (8 matières premières)
  - Détection automatique des seuils critiques (< 20 unités)
  - Logging complet des événements en format JSON
  - Simulation de consommation aléatoire (1-8 unités par cycle)

#### 2.2 Smart Contract Manager (`smart_contract.py`)
- **Fonction:** Gestion du cycle de vie des smart contracts
- **États des contrats:**
  - `DRAFT`: Contrat créé mais non initialisé
  - `PENDING_PRICE`: En attente de confirmation de prix
  - `READY_TO_EXECUTE`: Prêt pour exécution
  - `EXECUTED`: Contrat exécuté sur Hashgraph
  - `CANCELLED`: Contrat annulé

#### 2.3 Email Handler (`email_handler.py`)
- **Fonction:** Communication automatisée avec les fournisseurs
- **Fonctionnalités:**
  - Envoi d'emails HTML formatés via SMTP
  - Simulation de réponses fournisseurs avec prix aléatoires
  - Parsing automatique des réponses pour extraction de prix
  - Gestion des délais de réponse (24h)

#### 2.4 Hashgraph Client (`hashgraph_client.py`)
- **Fonction:** Interface avec le réseau Hedera Hashgraph
- **Implémentation:**
  - Client simulé pour démonstration (MockHederaClient)
  - Déploiement de contrats avec génération de hash de transaction
  - Vérification des déploiements
  - Gestion du compte et des frais de réseau

#### 2.5 Agentic Workflow (`hashgraph_client.py`)
- **Fonction:** Orchestration intelligente du processus complet
- **Workflow:**
  1. Extraction du prix depuis la réponse email
  2. Confirmation du prix dans le smart contract
  3. Exécution locale du contrat
  4. Déploiement sur Hedera Hashgraph
  5. Vérification du déploiement

## Choix Techniques

### 1. Langage et Frameworks
- **Python 3.8+**: Choisi pour sa simplicité et ses bibliothèques IoT
- **Hedera SDK Python**: Interface officielle pour Hashgraph
- **SMTP/IMAP**: Protocoles standards pour la communication email

### 2. Architecture de données
- **JSON**: Format d'échange pour tous les logs et exports
- **Dictionnaires Python**: Structures de données flexibles
- **Timestamps ISO 8601**: Standardisation des horodatages

### 3. Sécurité et Traçabilité
- **Hachage SHA-256**: Génération d'identifiants uniques
- **Logging complet**: Traçabilité de toutes les opérations
- **Validation des données**: Contrôles d'intégrité à chaque étape

### 4. Simulation vs Production
Le système utilise des composants simulés pour la démonstration :
- **RFID simulé**: Génération aléatoire de données de stock
- **Emails simulés**: Réponses automatiques avec prix aléatoires
- **Client Hedera simulé**: Mock du SDK pour éviter les frais de test

## Flux de Données

### 1. Cycle de surveillance principal
```
1. Lecture RFID → Détection seuil critique
2. Création smart contract → État DRAFT
3. Envoi email fournisseur → État PENDING_PRICE
4. Réception réponse → Extraction prix
5. Confirmation prix → État READY_TO_EXECUTE
6. Déploiement Hashgraph → État EXECUTED
```

### 2. Gestion des erreurs
- **Timeouts**: Annulation automatique après 24h
- **Retry logic**: Nouvelle tentative en cas d'échec
- **Logging d'erreurs**: Traçabilité complète des problèmes

## Métriques et Monitoring

### 1. Indicateurs système
- Nombre de cycles de surveillance
- Événements critiques détectés
- Contrats créés/exécutés
- Emails envoyés/reçus
- Taux de succès des déploiements

### 2. Exports et rapports
- **Logs RFID**: `logs/rfid_export_YYYYMMDD_HHMMSS.json`
- **Logs contrats**: `logs/contracts_export_YYYYMMDD_HHMMSS.json`
- **Logs emails**: `logs/email_logs_YYYYMMDD_HHMMSS.json`
- **Rapport complet**: `logs/complete_report_YYYYMMDD_HHMMSS.json`

## Tests et Validation

### 1. Tests unitaires (`tests/test_system.py`)
- **TestRFIDSimulator**: Validation de la simulation RFID
- **TestSmartContract**: Tests du cycle de vie des contrats
- **TestEmailHandler**: Validation de la communication email
- **TestHashgraphClient**: Tests du client Hashgraph

### 2. Couverture de tests
- Initialisation des composants
- Workflows complets
- Gestion d'erreurs
- Calculs de montants

## Déploiement et Configuration

### 1. Variables d'environnement (`.env`)
```bash
# Hedera Hashgraph
HEDERA_NETWORK=testnet
HEDERA_ACCOUNT_ID=0.0.123456
HEDERA_PRIVATE_KEY=302e...

# Email SMTP
SMTP_SERVER=smtp.gmail.com
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=app_password
SUPPLIER_EMAIL=<EMAIL>

# Simulation RFID
STOCK_THRESHOLD=20
RFID_SIMULATION_INTERVAL=5
```

### 2. Installation
```bash
pip install -r requirements.txt
cp .env.example .env
# Configurer les variables dans .env
python src/main.py
```

## Résultats et Démonstration

### 1. Fonctionnalités démontrées
✅ Simulation RFID avec détection de seuils critiques  
✅ Génération automatique de smart contracts  
✅ Envoi d'emails formatés aux fournisseurs  
✅ Simulation de réponses avec extraction de prix  
✅ Déploiement simulé sur Hedera Hashgraph  
✅ Workflow agentique complet  
✅ Logging et export de données  

### 2. Métriques de performance
- **Temps de cycle**: ~5 secondes par surveillance
- **Latence email**: 1-10 secondes (simulé)
- **Déploiement Hashgraph**: <1 seconde (simulé)
- **Précision détection**: 100% pour seuils < 20 unités

## Limitations et Améliorations

### 1. Limitations actuelles
- Simulation uniquement (pas de vrais tags RFID)
- Client Hedera simulé (pas de vrais déploiements)
- Emails simulés (pas de vraie communication SMTP)
- Pas d'interface utilisateur graphique

### 2. Améliorations futures
- Intégration avec de vrais lecteurs RFID
- Déploiement sur le vrai réseau Hedera
- Interface web pour monitoring
- Base de données pour persistance
- API REST pour intégration externe
- Notifications push/SMS
- Tableau de bord en temps réel

## Conclusion

Ce projet démontre avec succès l'intégration de technologies IoT (RFID), blockchain (Hedera Hashgraph), et d'intelligence artificielle (flux agentique) pour créer un système de gestion automatisée des stocks. L'architecture modulaire permet une évolutivité et une maintenance aisées, tandis que la simulation complète valide tous les concepts techniques requis.

Le système répond parfaitement aux objectifs du mini-projet en démontrant :
- L'utilisation de Hashgraph avec Python
- La génération dynamique de smart contracts
- L'automatisation complète du processus de commande
- La traçabilité et la sécurité des transactions
- Un flux agentique intelligent et réactif

---

**Note**: Ce rapport accompagne le code source complet disponible dans le répertoire du projet, incluant tous les modules, tests, et documentation technique.
