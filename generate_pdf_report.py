#!/usr/bin/env python3
"""
Script pour générer un rapport PDF à partir du fichier Markdown
"""
import os
import sys
import subprocess
from datetime import datetime

def check_pandoc():
    """Vérifie si Pandoc est installé"""
    try:
        result = subprocess.run(['pandoc', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Pandoc est installé")
            return True
        else:
            print("❌ Pandoc n'est pas installé")
            return False
    except FileNotFoundError:
        print("❌ Pandoc n'est pas trouvé dans le PATH")
        return False

def install_pandoc_instructions():
    """Affiche les instructions d'installation de Pandoc"""
    print("\n📋 Instructions d'installation de Pandoc :")
    print("="*50)
    print("Windows:")
    print("  1. Télécharger depuis : https://pandoc.org/installing.html")
    print("  2. Ou via chocolatey : choco install pandoc")
    print("  3. Ou via winget : winget install JohnMacFarlane.Pandoc")
    print("\nLinux (Ubuntu/Debian):")
    print("  sudo apt-get install pandoc")
    print("\nLinux (CentOS/RHEL):")
    print("  sudo yum install pandoc")
    print("\nmacOS:")
    print("  brew install pandoc")

def generate_pdf_with_pandoc():
    """Génère le PDF avec Pandoc"""
    input_file = "RAPPORT_PROJET_FINAL.md"
    output_file = f"Rapport_Projet_Stocks_RFID_Hashgraph_{datetime.now().strftime('%Y%m%d')}.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ Fichier {input_file} non trouvé")
        return False
    
    # Commande Pandoc avec options pour un PDF professionnel
    pandoc_cmd = [
        'pandoc',
        input_file,
        '-o', output_file,
        '--pdf-engine=xelatex',  # Moteur PDF pour meilleur rendu
        '--toc',                 # Table des matières
        '--toc-depth=3',         # Profondeur de la table des matières
        '--number-sections',     # Numérotation des sections
        '--highlight-style=github',  # Style de coloration syntaxique
        '-V', 'geometry:margin=2.5cm',  # Marges
        '-V', 'fontsize=11pt',   # Taille de police
        '-V', 'documentclass=article',  # Classe de document
        '-V', 'papersize=a4',    # Format papier
        '-V', 'lang=fr-FR'       # Langue française
    ]
    
    try:
        print(f"🔄 Génération du PDF : {output_file}")
        result = subprocess.run(pandoc_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ PDF généré avec succès : {output_file}")
            print(f"📄 Taille du fichier : {os.path.getsize(output_file)} bytes")
            return True
        else:
            print(f"❌ Erreur lors de la génération PDF :")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        return False

def generate_html_alternative():
    """Génère une version HTML comme alternative"""
    input_file = "RAPPORT_PROJET_FINAL.md"
    output_file = f"Rapport_Projet_Stocks_RFID_Hashgraph_{datetime.now().strftime('%Y%m%d')}.html"
    
    if not os.path.exists(input_file):
        print(f"❌ Fichier {input_file} non trouvé")
        return False
    
    # Commande Pandoc pour HTML
    pandoc_cmd = [
        'pandoc',
        input_file,
        '-o', output_file,
        '--standalone',          # Document HTML complet
        '--toc',                 # Table des matières
        '--toc-depth=3',         # Profondeur de la table des matières
        '--number-sections',     # Numérotation des sections
        '--highlight-style=github',  # Style de coloration
        '--css=style.css',       # CSS personnalisé (optionnel)
        '-V', 'lang=fr-FR'       # Langue française
    ]
    
    try:
        print(f"🔄 Génération du HTML : {output_file}")
        result = subprocess.run(pandoc_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ HTML généré avec succès : {output_file}")
            print(f"📄 Taille du fichier : {os.path.getsize(output_file)} bytes")
            return True
        else:
            print(f"❌ Erreur lors de la génération HTML :")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        return False

def create_simple_html():
    """Crée un HTML simple sans Pandoc"""
    input_file = "RAPPORT_PROJET_FINAL.md"
    output_file = f"Rapport_Projet_Simple_{datetime.now().strftime('%Y%m%d')}.html"
    
    if not os.path.exists(input_file):
        print(f"❌ Fichier {input_file} non trouvé")
        return False
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        # HTML simple avec le contenu Markdown
        html_content = f"""
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport de Projet - Suivi Intelligent des Stocks</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; border-bottom: 1px solid #bdc3c7; padding-bottom: 5px; }}
        h3 {{ color: #7f8c8d; }}
        code {{ background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }}
        pre {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }}
        .highlight {{ background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; }}
        .success {{ background-color: #d4edda; padding: 10px; border-left: 4px solid #28a745; }}
        .info {{ background-color: #d1ecf1; padding: 10px; border-left: 4px solid #17a2b8; }}
        table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .footer {{ margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <pre>{markdown_content}</pre>
        <div class="footer">
            <p>Rapport généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}</p>
            <p>Projet : Suivi Intelligent des Stocks avec RFID et Hashgraph</p>
        </div>
    </div>
</body>
</html>
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ HTML simple généré : {output_file}")
        print(f"📄 Taille du fichier : {os.path.getsize(output_file)} bytes")
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        return False

def main():
    """Fonction principale"""
    print("="*60)
    print("  GÉNÉRATEUR DE RAPPORT PDF")
    print("  Suivi Intelligent des Stocks avec RFID et Hashgraph")
    print("="*60)
    
    # Vérification de Pandoc
    if check_pandoc():
        print("\n🎯 Tentative de génération PDF avec Pandoc...")
        if generate_pdf_with_pandoc():
            print("\n🎉 Rapport PDF généré avec succès !")
            return
        else:
            print("\n⚠️ Échec PDF, tentative HTML...")
            if generate_html_alternative():
                print("\n🎉 Rapport HTML généré avec succès !")
                return
    else:
        install_pandoc_instructions()
    
    # Alternative sans Pandoc
    print("\n🔄 Génération d'un rapport HTML simple...")
    if create_simple_html():
        print("\n🎉 Rapport HTML simple généré !")
        print("\n💡 Pour un PDF professionnel, installez Pandoc et relancez ce script.")
    else:
        print("\n❌ Échec de génération du rapport")
    
    print("\n📋 Fichiers disponibles :")
    for file in os.listdir('.'):
        if file.startswith('Rapport_') and (file.endswith('.pdf') or file.endswith('.html')):
            print(f"  📄 {file}")

if __name__ == "__main__":
    main()
