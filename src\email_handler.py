"""
Gestionnaire d'emails pour les demandes de prix aux fournisseurs
"""
import smtplib
import imaplib
import email
import json
import re
import random
import time
import logging
from datetime import datetime, timedelta
from email.mime.text import MIMEText as MimeText
from email.mime.multipart import MI<PERSON>Multipart as Mime<PERSON>ultipart
from typing import Dict, Optional, List
from config.settings import Config

class EmailHandler:
    """Gestionnaire pour l'envoi et la réception d'emails"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.sent_requests: Dict[str, Dict] = {}
        self.received_responses: List[Dict] = []
    
    def _setup_logger(self) -> logging.Logger:
        """Configure le système de logging"""
        logger = logging.getLogger('EmailHandler')
        logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def send_price_request(self, contract_id: str, material_name: str, 
                          quantity: int, unit: str, supplier: str) -> bool:
        """Envoie une demande de prix par email"""
        try:
            # Création du message
            msg = MimeMultipart()
            msg['From'] = Config.EMAIL_ADDRESS
            msg['To'] = Config.SUPPLIER_EMAIL
            msg['Subject'] = f"Demande de prix - {material_name} (Contrat: {contract_id})"
            
            # Corps du message
            body = self._create_price_request_body(
                contract_id, material_name, quantity, unit, supplier
            )
            msg.attach(MimeText(body, 'html', 'utf-8'))
            
            # Envoi via SMTP
            with smtplib.SMTP(Config.SMTP_SERVER, Config.SMTP_PORT) as server:
                server.starttls()
                server.login(Config.EMAIL_ADDRESS, Config.EMAIL_PASSWORD)
                server.send_message(msg)
            
            # Enregistrement de la demande
            self.sent_requests[contract_id] = {
                "contract_id": contract_id,
                "material_name": material_name,
                "quantity": quantity,
                "unit": unit,
                "supplier": supplier,
                "sent_at": datetime.now().isoformat(),
                "status": "sent"
            }
            
            self.logger.info(
                f"Demande de prix envoyée - Contrat: {contract_id}, "
                f"Matière: {material_name}, Quantité: {quantity} {unit}"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'envoi de l'email: {str(e)}")
            return False
    
    def _create_price_request_body(self, contract_id: str, material_name: str, 
                                  quantity: int, unit: str, supplier: str) -> str:
        """Crée le corps HTML de la demande de prix"""
        return f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .header {{ background-color: #f0f0f0; padding: 20px; }}
                .content {{ padding: 20px; }}
                .footer {{ background-color: #f0f0f0; padding: 10px; font-size: 12px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>Demande de Prix Automatisée</h2>
                <p>Système de Gestion Intelligente des Stocks - RFID & Blockchain</p>
            </div>
            
            <div class="content">
                <p>Bonjour,</p>
                
                <p>Notre système de gestion automatisée des stocks a détecté un niveau critique 
                pour une matière première. Nous sollicitons votre meilleur prix pour la commande suivante :</p>
                
                <table>
                    <tr><th>Référence Contrat</th><td>{contract_id}</td></tr>
                    <tr><th>Matière Première</th><td>{material_name}</td></tr>
                    <tr><th>Quantité Demandée</th><td>{quantity} {unit}</td></tr>
                    <tr><th>Fournisseur</th><td>{supplier}</td></tr>
                    <tr><th>Date de Demande</th><td>{datetime.now().strftime('%d/%m/%Y %H:%M')}</td></tr>
                </table>
                
                <p><strong>Instructions pour la réponse :</strong></p>
                <ul>
                    <li>Merci de répondre à cet email avec votre prix unitaire</li>
                    <li>Indiquez la référence du contrat : <strong>{contract_id}</strong></li>
                    <li>Format attendu : "Prix: XX.XX €/{unit}"</li>
                    <li>Délai de réponse : 24 heures</li>
                </ul>
                
                <p>Cette demande est générée automatiquement par notre système IoT intégré 
                avec la blockchain Hedera Hashgraph pour garantir la traçabilité et la sécurité 
                des transactions.</p>
                
                <p>Cordialement,<br>
                Système Automatisé de Gestion des Stocks</p>
            </div>
            
            <div class="footer">
                <p>Ce message a été généré automatiquement. 
                Pour toute question, contactez notre service technique.</p>
            </div>
        </body>
        </html>
        """
    
    def simulate_supplier_response(self, contract_id: str, base_price: float = None) -> Dict:
        """Simule une réponse automatique du fournisseur avec un prix"""
        if contract_id not in self.sent_requests:
            raise ValueError(f"Aucune demande trouvée pour le contrat {contract_id}")
        
        request_data = self.sent_requests[contract_id]
        
        # Génération d'un prix simulé si non fourni
        if base_price is None:
            # Prix de base aléatoire entre 10 et 100 €
            base_price = random.uniform(10.0, 100.0)
        
        # Variation du prix ±15%
        price_variation = random.uniform(-0.15, 0.15)
        final_price = base_price * (1 + price_variation)
        final_price = round(final_price, 2)
        
        # Simulation d'un délai de réponse (1-10 secondes)
        response_delay = random.uniform(1, 10)
        time.sleep(response_delay)
        
        # Création de la réponse simulée
        response = {
            "contract_id": contract_id,
            "material_name": request_data["material_name"],
            "unit_price": final_price,
            "unit": request_data["unit"],
            "supplier": request_data["supplier"],
            "response_time": datetime.now().isoformat(),
            "validity_period": "48h",
            "delivery_time": f"{random.randint(3, 14)} jours",
            "response_type": "simulated"
        }
        
        self.received_responses.append(response)
        self.sent_requests[contract_id]["status"] = "responded"
        self.sent_requests[contract_id]["response_received_at"] = response["response_time"]
        
        self.logger.info(
            f"Réponse simulée reçue - Contrat: {contract_id}, "
            f"Prix: {final_price} €/{request_data['unit']}"
        )
        
        return response
    
    def parse_real_email_response(self, email_content: str, contract_id: str) -> Optional[Dict]:
        """Parse une vraie réponse email pour extraire le prix"""
        try:
            # Recherche de patterns de prix dans l'email
            price_patterns = [
                r'prix[:\s]*(\d+[.,]\d+)',
                r'price[:\s]*(\d+[.,]\d+)',
                r'(\d+[.,]\d+)\s*€',
                r'(\d+[.,]\d+)\s*euros?',
                r'(\d+[.,]\d+)\s*/\s*\w+'
            ]
            
            for pattern in price_patterns:
                match = re.search(pattern, email_content.lower())
                if match:
                    price_str = match.group(1).replace(',', '.')
                    price = float(price_str)
                    
                    response = {
                        "contract_id": contract_id,
                        "unit_price": price,
                        "response_time": datetime.now().isoformat(),
                        "response_type": "real_email",
                        "raw_content": email_content[:200] + "..." if len(email_content) > 200 else email_content
                    }
                    
                    self.received_responses.append(response)
                    self.logger.info(f"Prix extrait de l'email: {price} € pour contrat {contract_id}")
                    
                    return response
            
            self.logger.warning(f"Aucun prix trouvé dans l'email pour le contrat {contract_id}")
            return None
            
        except Exception as e:
            self.logger.error(f"Erreur lors du parsing de l'email: {str(e)}")
            return None
    
    def get_response_for_contract(self, contract_id: str) -> Optional[Dict]:
        """Récupère la réponse pour un contrat donné"""
        for response in self.received_responses:
            if response["contract_id"] == contract_id:
                return response
        return None
    
    def get_pending_requests(self) -> List[Dict]:
        """Retourne les demandes en attente de réponse"""
        return [req for req in self.sent_requests.values() if req["status"] == "sent"]
    
    def export_email_logs(self, filename: Optional[str] = None) -> str:
        """Exporte les logs d'emails en JSON"""
        if filename is None:
            filename = f"logs/email_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "sent_requests": list(self.sent_requests.values()),
            "received_responses": self.received_responses,
            "statistics": {
                "total_sent": len(self.sent_requests),
                "total_responses": len(self.received_responses),
                "pending_requests": len(self.get_pending_requests())
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Logs d'emails exportés vers: {filename}")
        return filename

if __name__ == "__main__":
    # Test du gestionnaire d'emails
    email_handler = EmailHandler()
    
    print("=== Test du Gestionnaire d'Emails ===")
    
    # Test d'envoi de demande (simulation)
    contract_id = "TEST_CONTRACT_001"
    
    print(f"Envoi d'une demande de prix pour le contrat: {contract_id}")
    
    # Simulation d'envoi (sans vraiment envoyer)
    email_handler.sent_requests[contract_id] = {
        "contract_id": contract_id,
        "material_name": "Acier inoxydable",
        "quantity": 50,
        "unit": "kg",
        "supplier": "MetalCorp",
        "sent_at": datetime.now().isoformat(),
        "status": "sent"
    }
    
    # Simulation de réponse
    response = email_handler.simulate_supplier_response(contract_id, base_price=15.50)
    print(f"Réponse reçue: {response['unit_price']} €/{response['unit']}")
    
    # Export des logs
    log_file = email_handler.export_email_logs()
    print(f"Logs exportés: {log_file}")
