"""
Tests unitaires pour le système de gestion intelligente des stocks
"""
import unittest
import sys
import os

# Ajout du répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from rfid_simulator import RFIDSimulator, RFIDTag
from smart_contract import SmartContractManager, SmartContract, ContractStatus
from email_handler import EmailHandler
from hashgraph_client import HashgraphClient

class TestRFIDSimulator(unittest.TestCase):
    """Tests pour le simulateur RFID"""
    
    def setUp(self):
        self.simulator = RFIDSimulator()
    
    def test_tag_initialization(self):
        """Test de l'initialisation des tags RFID"""
        self.assertGreater(len(self.simulator.tags), 0)
        
        for tag in self.simulator.tags:
            self.assertIsInstance(tag, RFIDTag)
            self.assertIsNotNone(tag.tag_id)
            self.assertIsNotNone(tag.material_name)
            self.assertGreater(tag.current_quantity, 0)
    
    def test_stock_consumption(self):
        """Test de la simulation de consommation"""
        initial_quantities = [tag.current_quantity for tag in self.simulator.tags]
        
        critical_events = self.simulator.simulate_stock_consumption()
        
        # Vérifier que les quantités ont changé
        for i, tag in enumerate(self.simulator.tags):
            self.assertLessEqual(tag.current_quantity, initial_quantities[i])
    
    def test_critical_detection(self):
        """Test de la détection des seuils critiques"""
        # Forcer un tag en situation critique
        test_tag = self.simulator.tags[0]
        test_tag.update_quantity(5)  # En dessous du seuil de 20
        
        critical_materials = self.simulator.get_critical_materials()
        self.assertIn(test_tag, critical_materials)

class TestSmartContract(unittest.TestCase):
    """Tests pour les smart contracts"""
    
    def setUp(self):
        self.manager = SmartContractManager()
    
    def test_contract_creation(self):
        """Test de création de contrat"""
        contract = self.manager.create_contract(
            material_name="Test Material",
            quantity_to_order=100,
            unit="kg",
            supplier="Test Supplier",
            provisional_price=10.0
        )
        
        self.assertIsInstance(contract, SmartContract)
        self.assertEqual(contract.material_name, "Test Material")
        self.assertEqual(contract.quantity_to_order, 100)
        self.assertEqual(contract.status, ContractStatus.DRAFT)
    
    def test_price_confirmation_workflow(self):
        """Test du workflow de confirmation de prix"""
        contract = self.manager.create_contract(
            material_name="Test Material",
            quantity_to_order=100,
            unit="kg",
            supplier="Test Supplier"
        )
        
        # Définition du prix provisoire
        contract.set_provisional_price(15.0)
        self.assertEqual(contract.status, ContractStatus.PENDING_PRICE)
        
        # Confirmation du prix
        success = contract.confirm_price(16.5)
        self.assertTrue(success)
        self.assertEqual(contract.status, ContractStatus.READY_TO_EXECUTE)
        self.assertEqual(contract.confirmed_price, 16.5)
        
        # Exécution du contrat
        execution_hash = contract.execute_contract()
        self.assertIsNotNone(execution_hash)
        self.assertEqual(contract.status, ContractStatus.EXECUTED)
    
    def test_contract_total_amount(self):
        """Test du calcul du montant total"""
        contract = SmartContract(
            material_name="Test",
            quantity_to_order=50,
            unit="kg",
            supplier="Test Supplier"
        )
        
        contract.confirm_price(20.0)
        total = contract.get_total_amount()
        self.assertEqual(total, 1000.0)  # 50 * 20.0

class TestEmailHandler(unittest.TestCase):
    """Tests pour le gestionnaire d'emails"""
    
    def setUp(self):
        self.email_handler = EmailHandler()
    
    def test_price_request_simulation(self):
        """Test de simulation de demande de prix"""
        contract_id = "TEST_001"
        
        # Simulation d'envoi (ajout direct à la liste)
        self.email_handler.sent_requests[contract_id] = {
            "contract_id": contract_id,
            "material_name": "Test Material",
            "quantity": 100,
            "unit": "kg",
            "supplier": "Test Supplier",
            "sent_at": "2024-01-01T00:00:00",
            "status": "sent"
        }
        
        # Test de réponse simulée
        response = self.email_handler.simulate_supplier_response(contract_id, 15.0)
        
        self.assertEqual(response["contract_id"], contract_id)
        self.assertIsInstance(response["unit_price"], float)
        self.assertIn("response_time", response)
    
    def test_price_extraction(self):
        """Test d'extraction de prix depuis un email"""
        test_email = "Bonjour, notre prix pour ce produit est de 25.50 € par kg."
        
        response = self.email_handler.parse_real_email_response(test_email, "TEST_002")
        
        if response:  # Si l'extraction fonctionne
            self.assertEqual(response["unit_price"], 25.50)

class TestHashgraphClient(unittest.TestCase):
    """Tests pour le client Hedera Hashgraph"""
    
    def setUp(self):
        self.client = HashgraphClient()
    
    def test_client_initialization(self):
        """Test d'initialisation du client"""
        self.assertIsNotNone(self.client.client)
        self.assertTrue(self.client.client.is_connected)
    
    def test_contract_deployment(self):
        """Test de déploiement de contrat"""
        test_contract = {
            "contract_id": "TEST_001",
            "material_name": "Test Material",
            "quantity": 100,
            "unit": "kg",
            "supplier": "Test Supplier",
            "unit_price": 15.0,
            "total_amount": 1500.0
        }
        
        transaction_hash = self.client.deploy_purchase_order(test_contract)
        
        self.assertIsNotNone(transaction_hash)
        self.assertIsInstance(transaction_hash, str)
        
        # Vérification du déploiement
        verification = self.client.verify_deployment(transaction_hash)
        self.assertTrue(verification)
    
    def test_account_info(self):
        """Test de récupération des informations de compte"""
        account_info = self.client.get_account_info()
        
        self.assertIn("account_id", account_info)
        self.assertIn("network", account_info)
        self.assertIn("balance", account_info)

def run_tests():
    """Lance tous les tests"""
    print("=== Exécution des Tests Unitaires ===")
    
    # Création de la suite de tests
    test_suite = unittest.TestSuite()
    
    # Ajout des classes de tests
    test_classes = [
        TestRFIDSimulator,
        TestSmartContract,
        TestEmailHandler,
        TestHashgraphClient
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Exécution des tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Résumé
    print(f"\n=== Résumé des Tests ===")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.failures:
        print("\nÉchecs:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErreurs:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
