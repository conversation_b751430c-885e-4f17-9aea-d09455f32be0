"""
Client Hedera Hashgraph pour le déploiement des smart contracts
"""
import json
import logging
import hashlib
from datetime import datetime
from typing import Dict, Optional, List
from config.settings import Config

# Simulation du SDK Hedera (remplace hedera-sdk-python pour la démo)
class MockHederaClient:
    """Client simulé pour Hedera Hashgraph (pour démonstration)"""
    
    def __init__(self, network: str, account_id: str, private_key: str):
        self.network = network
        self.account_id = account_id
        self.private_key = private_key
        self.is_connected = False
        self.deployed_contracts: Dict[str, Dict] = {}
        self.transaction_history: List[Dict] = []
    
    def connect(self) -> bool:
        """Simule la connexion au réseau Hedera"""
        self.is_connected = True
        return True
    
    def deploy_contract(self, contract_data: Dict) -> str:
        """Simule le déploiement d'un contrat sur Hedera"""
        if not self.is_connected:
            raise ConnectionError("Client non connecté au réseau Hedera")
        
        # Génération d'un hash de transaction simulé
        transaction_data = {
            "contract_data": contract_data,
            "timestamp": datetime.now().isoformat(),
            "account_id": self.account_id,
            "network": self.network
        }
        
        transaction_string = json.dumps(transaction_data, sort_keys=True)
        transaction_hash = hashlib.sha256(transaction_string.encode()).hexdigest()
        
        # Stockage du contrat déployé
        self.deployed_contracts[transaction_hash] = {
            "transaction_hash": transaction_hash,
            "contract_data": contract_data,
            "deployed_at": datetime.now().isoformat(),
            "status": "deployed",
            "gas_used": 75000,  # Simulation
            "network_fee": 0.001  # Simulation en HBAR
        }
        
        # Ajout à l'historique
        self.transaction_history.append(self.deployed_contracts[transaction_hash])
        
        return transaction_hash
    
    def get_contract(self, transaction_hash: str) -> Optional[Dict]:
        """Récupère un contrat déployé"""
        return self.deployed_contracts.get(transaction_hash)
    
    def get_account_balance(self) -> float:
        """Simule la récupération du solde du compte"""
        return 1000.0  # Simulation en HBAR

class HashgraphClient:
    """Client pour interagir avec Hedera Hashgraph"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.client = None
        self._initialize_client()
    
    def _setup_logger(self) -> logging.Logger:
        """Configure le système de logging"""
        logger = logging.getLogger('HashgraphClient')
        logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def _initialize_client(self):
        """Initialise le client Hedera"""
        try:
            # Utilisation du client simulé pour la démonstration
            self.client = MockHederaClient(
                network=Config.HEDERA_NETWORK,
                account_id=Config.HEDERA_ACCOUNT_ID,
                private_key=Config.HEDERA_PRIVATE_KEY
            )
            
            # Connexion au réseau
            if self.client.connect():
                self.logger.info(f"Connecté au réseau Hedera: {Config.HEDERA_NETWORK}")
            else:
                raise ConnectionError("Impossible de se connecter au réseau Hedera")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation du client Hedera: {str(e)}")
            raise
    
    def deploy_purchase_order(self, contract_data: Dict) -> str:
        """Déploie un bon de commande sur Hedera Hashgraph"""
        try:
            # Validation des données du contrat
            required_fields = [
                'contract_id', 'material_name', 'quantity', 'unit', 
                'supplier', 'unit_price', 'total_amount'
            ]
            
            for field in required_fields:
                if field not in contract_data:
                    raise ValueError(f"Champ requis manquant: {field}")
            
            # Préparation des données pour Hedera
            hedera_contract = {
                "contract_type": "purchase_order",
                "version": "1.0",
                "created_at": datetime.now().isoformat(),
                "data": contract_data,
                "metadata": {
                    "deployer_account": Config.HEDERA_ACCOUNT_ID,
                    "network": Config.HEDERA_NETWORK,
                    "gas_limit": Config.CONTRACT_GAS_LIMIT
                }
            }
            
            # Déploiement sur Hedera
            transaction_hash = self.client.deploy_contract(hedera_contract)
            
            self.logger.info(
                f"Contrat déployé sur Hedera - Hash: {transaction_hash}, "
                f"Contrat ID: {contract_data['contract_id']}"
            )
            
            return transaction_hash
            
        except Exception as e:
            self.logger.error(f"Erreur lors du déploiement sur Hedera: {str(e)}")
            raise
    
    def verify_deployment(self, transaction_hash: str) -> bool:
        """Vérifie qu'un contrat a été correctement déployé"""
        try:
            contract = self.client.get_contract(transaction_hash)
            if contract and contract["status"] == "deployed":
                self.logger.info(f"Déploiement vérifié pour le hash: {transaction_hash}")
                return True
            else:
                self.logger.warning(f"Contrat non trouvé ou non déployé: {transaction_hash}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification: {str(e)}")
            return False
    
    def get_deployment_info(self, transaction_hash: str) -> Optional[Dict]:
        """Récupère les informations de déploiement d'un contrat"""
        try:
            return self.client.get_contract(transaction_hash)
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des infos: {str(e)}")
            return None
    
    def get_account_info(self) -> Dict:
        """Récupère les informations du compte Hedera"""
        try:
            balance = self.client.get_account_balance()
            
            return {
                "account_id": Config.HEDERA_ACCOUNT_ID,
                "network": Config.HEDERA_NETWORK,
                "balance": balance,
                "currency": "HBAR",
                "total_deployments": len(self.client.deployed_contracts),
                "last_activity": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des infos compte: {str(e)}")
            return {}
    
    def get_deployment_history(self) -> List[Dict]:
        """Récupère l'historique des déploiements"""
        try:
            return self.client.transaction_history
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de l'historique: {str(e)}")
            return []
    
    def export_deployment_logs(self, filename: Optional[str] = None) -> str:
        """Exporte les logs de déploiement"""
        if filename is None:
            filename = f"logs/hedera_deployments_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "account_info": self.get_account_info(),
            "deployment_history": self.get_deployment_history(),
            "total_deployments": len(self.client.deployed_contracts)
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Logs Hedera exportés vers: {filename}")
        return filename

class AgenticWorkflow:
    """Flux agentique pour l'orchestration complète du processus"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.hashgraph_client = HashgraphClient()
        self.workflow_history: List[Dict] = []
    
    def _setup_logger(self) -> logging.Logger:
        """Configure le système de logging"""
        logger = logging.getLogger('AgenticWorkflow')
        logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def process_price_confirmation(self, contract, email_response: Dict) -> str:
        """Traite la confirmation de prix et déploie le contrat final"""
        try:
            workflow_step = {
                "step": "price_confirmation_processing",
                "started_at": datetime.now().isoformat(),
                "contract_id": contract.contract_id,
                "email_response": email_response
            }
            
            # 1. Extraction du prix depuis la réponse email
            confirmed_price = email_response["unit_price"]
            self.logger.info(f"Prix extrait: {confirmed_price} €/{contract.unit}")
            
            # 2. Confirmation du prix dans le contrat
            if not contract.confirm_price(confirmed_price):
                raise ValueError("Impossible de confirmer le prix dans le contrat")
            
            # 3. Exécution du contrat local
            execution_hash = contract.execute_contract()
            self.logger.info(f"Contrat exécuté localement: {execution_hash}")
            
            # 4. Préparation des données pour Hedera
            contract_data = {
                "contract_id": contract.contract_id,
                "material_name": contract.material_name,
                "quantity": contract.quantity_to_order,
                "unit": contract.unit,
                "supplier": contract.supplier,
                "unit_price": confirmed_price,
                "total_amount": contract.get_total_amount(),
                "execution_hash": execution_hash,
                "email_confirmation": {
                    "response_time": email_response["response_time"],
                    "supplier_response": email_response.get("response_type", "unknown")
                }
            }
            
            # 5. Déploiement sur Hedera Hashgraph
            hedera_hash = self.hashgraph_client.deploy_purchase_order(contract_data)
            
            # 6. Vérification du déploiement
            if self.hashgraph_client.verify_deployment(hedera_hash):
                self.logger.info(f"Déploiement Hedera confirmé: {hedera_hash}")
                
                workflow_step.update({
                    "status": "success",
                    "completed_at": datetime.now().isoformat(),
                    "hedera_hash": hedera_hash,
                    "execution_hash": execution_hash,
                    "total_amount": contract.get_total_amount()
                })
                
            else:
                raise Exception("Échec de la vérification du déploiement Hedera")
            
            self.workflow_history.append(workflow_step)
            
            return hedera_hash
            
        except Exception as e:
            workflow_step.update({
                "status": "error",
                "error": str(e),
                "completed_at": datetime.now().isoformat()
            })
            self.workflow_history.append(workflow_step)
            
            self.logger.error(f"Erreur dans le flux agentique: {str(e)}")
            raise
    
    def get_workflow_status(self, contract_id: str) -> Optional[Dict]:
        """Récupère le statut du workflow pour un contrat"""
        for step in reversed(self.workflow_history):
            if step.get("contract_id") == contract_id:
                return step
        return None
    
    def export_workflow_logs(self, filename: Optional[str] = None) -> str:
        """Exporte les logs du workflow"""
        if filename is None:
            filename = f"logs/workflow_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "workflow_history": self.workflow_history,
            "total_workflows": len(self.workflow_history),
            "successful_workflows": len([w for w in self.workflow_history if w.get("status") == "success"])
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Logs workflow exportés vers: {filename}")
        return filename

if __name__ == "__main__":
    # Test du client Hashgraph et du workflow
    print("=== Test du Client Hedera Hashgraph ===")
    
    # Test du client
    client = HashgraphClient()
    account_info = client.get_account_info()
    print(f"Compte Hedera: {account_info}")
    
    # Test du workflow
    workflow = AgenticWorkflow()
    print("Workflow agentique initialisé")
    
    # Simulation d'un contrat de test
    test_contract_data = {
        "contract_id": "TEST_001",
        "material_name": "Acier inoxydable",
        "quantity": 50,
        "unit": "kg",
        "supplier": "MetalCorp",
        "unit_price": 16.20,
        "total_amount": 810.0
    }
    
    # Test de déploiement
    hedera_hash = client.deploy_purchase_order(test_contract_data)
    print(f"Contrat déployé sur Hedera: {hedera_hash}")
    
    # Vérification
    if client.verify_deployment(hedera_hash):
        print("Déploiement vérifié avec succès")
    
    # Export des logs
    log_file = client.export_deployment_logs()
    print(f"Logs exportés: {log_file}")
