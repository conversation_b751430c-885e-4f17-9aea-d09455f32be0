"""
Configuration centralisée pour le projet de suivi intelligent des stocks
"""
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

class Config:
    """Configuration principale du projet"""
    
    # Configuration Hedera Hashgraph
    HEDERA_NETWORK = os.getenv('HEDERA_NETWORK', 'testnet')
    HEDERA_ACCOUNT_ID = os.getenv('HEDERA_ACCOUNT_ID')
    HEDERA_PRIVATE_KEY = os.getenv('HEDERA_PRIVATE_KEY')
    
    # Configuration Email SMTP
    SMTP_SERVER = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    SMTP_PORT = int(os.getenv('SMTP_PORT', 587))
    EMAIL_ADDRESS = os.getenv('EMAIL_ADDRESS')
    EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
    SUPPLIER_EMAIL = os.getenv('SUPPLIER_EMAIL')
    
    # Configuration RFID Simulation
    RFID_SIMULATION_INTERVAL = int(os.getenv('RFID_SIMULATION_INTERVAL', 5))
    STOCK_THRESHOLD = int(os.getenv('STOCK_THRESHOLD', 20))
    INITIAL_STOCK_MIN = int(os.getenv('INITIAL_STOCK_MIN', 10))
    INITIAL_STOCK_MAX = int(os.getenv('INITIAL_STOCK_MAX', 50))
    
    # Configuration Smart Contract
    CONTRACT_GAS_LIMIT = int(os.getenv('CONTRACT_GAS_LIMIT', 100000))
    CONTRACT_INITIAL_BALANCE = int(os.getenv('CONTRACT_INITIAL_BALANCE', **********))
    
    # Configuration Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/stock_management.log')
    
    @classmethod
    def validate_config(cls):
        """Valide que toutes les configurations requises sont présentes"""
        required_vars = [
            'HEDERA_ACCOUNT_ID',
            'HEDERA_PRIVATE_KEY',
            'EMAIL_ADDRESS',
            'EMAIL_PASSWORD',
            'SUPPLIER_EMAIL'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Variables d'environnement manquantes: {', '.join(missing_vars)}")
        
        return True

# Matières premières disponibles pour la simulation
MATERIALS = [
    {"name": "Acier inoxydable", "unit": "kg", "supplier": "MetalCorp"},
    {"name": "Aluminium", "unit": "kg", "supplier": "AlumTech"},
    {"name": "Cuivre", "unit": "kg", "supplier": "CopperInc"},
    {"name": "Plastique PVC", "unit": "kg", "supplier": "PlasticPro"},
    {"name": "Caoutchouc", "unit": "kg", "supplier": "RubberCorp"},
    {"name": "Verre trempé", "unit": "m²", "supplier": "GlassTech"},
    {"name": "Bois de chêne", "unit": "m³", "supplier": "WoodMaster"},
    {"name": "Textile coton", "unit": "m", "supplier": "TextilePro"}
]
