"""
Point d'entrée principal du système de suivi intelligent des stocks
Intégration RFID, Smart Contracts et Hedera Hashgraph
"""
import time
import logging
import json
from datetime import datetime
from typing import List, Dict

from config.settings import Config
from rfid_simulator import RFIDSimulator
from smart_contract import SmartContractManager
from email_handler import EmailHandler
from hashgraph_client import AgenticWorkflow

class StockManagementSystem:
    """Système principal de gestion intelligente des stocks"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.rfid_simulator = RFIDSimulator()
        self.contract_manager = SmartContractManager()
        self.email_handler = EmailHandler()
        self.workflow = AgenticWorkflow()
        self.system_stats = {
            "started_at": datetime.now().isoformat(),
            "total_cycles": 0,
            "critical_events": 0,
            "contracts_created": 0,
            "contracts_executed": 0,
            "emails_sent": 0
        }
    
    def _setup_logger(self) -> logging.Logger:
        """Configure le système de logging principal"""
        logger = logging.getLogger('StockManagementSystem')
        logger.setLevel(getattr(logging, Config.LOG_LEVEL))
        
        # Handler pour fichier
        file_handler = logging.FileHandler(Config.LOG_FILE)
        file_handler.setLevel(logging.INFO)
        
        # Handler pour console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Format des logs
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def initialize_system(self):
        """Initialise le système et valide la configuration"""
        self.logger.info("=== Initialisation du Système de Gestion Intelligente des Stocks ===")
        
        try:
            # Validation de la configuration
            Config.validate_config()
            self.logger.info("Configuration validée avec succès")
            
            # Affichage des informations système
            self.logger.info(f"Réseau Hedera: {Config.HEDERA_NETWORK}")
            self.logger.info(f"Seuil critique: {Config.STOCK_THRESHOLD} unités")
            self.logger.info(f"Intervalle simulation: {Config.RFID_SIMULATION_INTERVAL} secondes")
            
            # Initialisation des composants
            self.logger.info(f"Tags RFID initialisés: {len(self.rfid_simulator.tags)}")
            self.logger.info("Système prêt à fonctionner")
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation: {str(e)}")
            raise
    
    def process_critical_materials(self, critical_materials: List) -> List[str]:
        """Traite les matières premières en situation critique"""
        processed_contracts = []
        
        for material in critical_materials:
            try:
                # Calcul de la quantité à commander (2x le seuil pour éviter les ruptures fréquentes)
                quantity_to_order = Config.STOCK_THRESHOLD * 2
                
                # Création du smart contract initial
                contract = self.contract_manager.create_contract(
                    material_name=material.material_name,
                    quantity_to_order=quantity_to_order,
                    unit=material.unit,
                    supplier=material.supplier,
                    provisional_price=0.0
                )
                
                self.system_stats["contracts_created"] += 1
                
                # Envoi de la demande de prix par email
                email_sent = self.email_handler.send_price_request(
                    contract.contract_id,
                    material.material_name,
                    quantity_to_order,
                    material.unit,
                    material.supplier
                )
                
                if email_sent:
                    self.system_stats["emails_sent"] += 1
                    contract.set_provisional_price(0.0)  # En attente de prix
                    
                    self.logger.info(
                        f"Processus initié pour {material.material_name} - "
                        f"Contrat: {contract.contract_id}"
                    )
                    
                    processed_contracts.append(contract.contract_id)
                else:
                    self.logger.error(f"Échec envoi email pour {material.material_name}")
                    contract.cancel_contract("Échec envoi email")
                
            except Exception as e:
                self.logger.error(f"Erreur traitement matière {material.material_name}: {str(e)}")
        
        return processed_contracts
    
    def process_email_responses(self):
        """Traite les réponses email et finalise les contrats"""
        pending_contracts = self.contract_manager.get_pending_contracts()
        
        for contract in pending_contracts:
            try:
                # Simulation de réponse email (en production, on lirait les vrais emails)
                email_response = self.email_handler.simulate_supplier_response(
                    contract.contract_id
                )
                
                if email_response:
                    # Traitement via le workflow agentique
                    hedera_hash = self.workflow.process_price_confirmation(
                        contract, email_response
                    )
                    
                    self.system_stats["contracts_executed"] += 1
                    
                    self.logger.info(
                        f"Contrat finalisé - ID: {contract.contract_id}, "
                        f"Hedera Hash: {hedera_hash}, "
                        f"Montant: {contract.get_total_amount():.2f} €"
                    )
                
            except Exception as e:
                self.logger.error(f"Erreur traitement réponse pour {contract.contract_id}: {str(e)}")
    
    def run_monitoring_cycle(self):
        """Exécute un cycle de surveillance complet"""
        self.logger.info("--- Début du cycle de surveillance ---")
        
        # 1. Simulation de lecture RFID
        critical_events = self.rfid_simulator.simulate_stock_consumption()
        
        if critical_events:
            self.system_stats["critical_events"] += len(critical_events)
            
            # 2. Récupération des matières critiques
            critical_materials = self.rfid_simulator.get_critical_materials()
            
            self.logger.warning(
                f"{len(critical_materials)} matière(s) en situation critique détectée(s)"
            )
            
            # 3. Traitement des matières critiques
            processed_contracts = self.process_critical_materials(critical_materials)
            
            if processed_contracts:
                self.logger.info(f"{len(processed_contracts)} contrat(s) créé(s)")
        
        # 4. Traitement des réponses email en attente
        self.process_email_responses()
        
        # 5. Nettoyage des contrats expirés
        expired_count = self.contract_manager.cleanup_expired_contracts()
        if expired_count > 0:
            self.logger.info(f"{expired_count} contrat(s) expiré(s) nettoyé(s)")
        
        self.system_stats["total_cycles"] += 1
        self.logger.info("--- Fin du cycle de surveillance ---")
    
    def display_system_status(self):
        """Affiche le statut complet du système"""
        print("\n" + "="*60)
        print("           STATUT DU SYSTÈME")
        print("="*60)
        
        # Statistiques générales
        print(f"Cycles exécutés: {self.system_stats['total_cycles']}")
        print(f"Événements critiques: {self.system_stats['critical_events']}")
        print(f"Contrats créés: {self.system_stats['contracts_created']}")
        print(f"Contrats exécutés: {self.system_stats['contracts_executed']}")
        print(f"Emails envoyés: {self.system_stats['emails_sent']}")
        
        # Statut des stocks
        stock_status = self.rfid_simulator.get_stock_status()
        print(f"\nMatières surveillées: {stock_status['total_materials']}")
        print(f"Matières critiques: {stock_status['critical_materials']}")
        print(f"Pourcentage critique: {stock_status['critical_percentage']:.1f}%")
        
        # Statut des contrats
        contract_stats = self.contract_manager.get_statistics()
        print(f"\nContrats actifs: {contract_stats['total_contracts']}")
        print(f"Valeur totale: {contract_stats['total_value']:.2f} €")
        
        # Compte Hedera
        account_info = self.workflow.hashgraph_client.get_account_info()
        print(f"\nCompte Hedera: {account_info.get('account_id', 'N/A')}")
        print(f"Solde: {account_info.get('balance', 0):.3f} HBAR")
        print(f"Déploiements: {account_info.get('total_deployments', 0)}")
        
        print("="*60)
    
    def export_complete_report(self) -> str:
        """Exporte un rapport complet du système"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"logs/complete_report_{timestamp}.json"
        
        report_data = {
            "report_timestamp": datetime.now().isoformat(),
            "system_statistics": self.system_stats,
            "stock_status": self.rfid_simulator.get_stock_status(),
            "contract_statistics": self.contract_manager.get_statistics(),
            "account_info": self.workflow.hashgraph_client.get_account_info(),
            "deployment_history": self.workflow.hashgraph_client.get_deployment_history()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Rapport complet exporté: {filename}")
        return filename
    
    def run_demo(self, duration_minutes: int = 2):
        """Lance une démonstration du système"""
        self.logger.info(f"=== DÉMONSTRATION DU SYSTÈME ({duration_minutes} minutes) ===")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        try:
            while time.time() < end_time:
                self.run_monitoring_cycle()
                self.display_system_status()
                time.sleep(Config.RFID_SIMULATION_INTERVAL)
            
            # Rapport final
            self.logger.info("=== FIN DE LA DÉMONSTRATION ===")
            report_file = self.export_complete_report()
            print(f"\nRapport final généré: {report_file}")
            
        except KeyboardInterrupt:
            self.logger.info("Démonstration interrompue par l'utilisateur")
        except Exception as e:
            self.logger.error(f"Erreur durant la démonstration: {str(e)}")

def main():
    """Fonction principale"""
    try:
        # Initialisation du système
        system = StockManagementSystem()
        system.initialize_system()
        
        # Lancement de la démonstration
        system.run_demo(duration_minutes=2)
        
    except Exception as e:
        logging.error(f"Erreur fatale: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
