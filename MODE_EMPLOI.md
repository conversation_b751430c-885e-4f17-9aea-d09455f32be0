# 📖 MODE D'EMPLOI - Suivi Intelligent des Stocks avec RFID et Hashgraph

**Auteur :** <PERSON><PERSON><PERSON><PERSON> ZOUHAIR  
**Version :** 1.0  
**Date :** Juillet 2025

---

## 🎯 PRÉSENTATION DE L'APPLICATION

### **Qu'est-ce que c'est ?**
Cette application simule un système intelligent de gestion des stocks qui combine :
- **Capteurs RFID** pour surveiller les niveaux de stock en temps réel
- **Smart Contracts** pour automatiser les commandes
- **Blockchain Hedera Hashgraph** pour sécuriser les transactions
- **Intelligence Artificielle** pour orchestrer tout le processus

### **Que fait l'application ?**
1. 🏷️ **Surveille** 8 types de matières premières avec des tags RFID virtuels
2. ⚠️ **Détecte** automatiquement quand le stock devient critique (< 20 unités)
3. 📄 **Crée** automatiquement des smart contracts pour commander
4. 📧 **Envoie** des emails aux fournisseurs pour demander les prix
5. 🤖 **Traite** les réponses et finalise les commandes
6. 🔗 **Déploie** les contrats sur la blockchain Hedera Hashgraph

---

## 🚀 DÉMARRAGE RAPIDE

### **Étape 1 : Vérification**
Assurez-vous que Python est installé :
```bash
py -3 --version
```
*Résultat attendu : Python 3.8.8 ou supérieur*

### **Étape 2 : Test Rapide (30 secondes)**
```bash
py -3 test_simple.py
```
*Ce test vérifie que tout fonctionne correctement*

### **Étape 3 : Première Démonstration**
```bash
py -3 demo.py
```
*Démonstration interactive qui vous guide étape par étape*

---

## 🎮 MODES D'UTILISATION

### **Mode 1 : Test de Validation** ⚡
```bash
py -3 test_simple.py
```
**Durée :** 30 secondes  
**Objectif :** Vérifier que tous les composants fonctionnent  
**Résultat :** Affiche "TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!"

### **Mode 2 : Démonstration Interactive** 🎬
```bash
py -3 demo.py
```
**Durée :** 5-10 minutes  
**Objectif :** Comprendre le fonctionnement étape par étape  
**Interaction :** Appuyez sur Entrée pour passer à l'étape suivante

### **Mode 3 : Système Automatique** 🔄
```bash
py -3 run_system.py
```
**Durée :** 2 minutes  
**Objectif :** Voir le système fonctionner en mode production  
**Résultat :** Génère des contrats et des logs automatiquement

### **Mode 4 : Consultation du Rapport** 📖
```bash
py -3 open_report.py
```
**Objectif :** Ouvrir le rapport technique dans votre navigateur

---

## 🔍 COMPRENDRE LE FONCTIONNEMENT

### **1. Simulation RFID** 🏷️

**Que se passe-t-il ?**
- L'application simule 8 lecteurs RFID attachés à différentes matières premières
- Chaque matière a une quantité initiale aléatoire (10-50 unités)
- Toutes les 5 secondes, le système simule une consommation (1-8 unités)

**Matières surveillées :**
- Acier inoxydable (kg)
- Aluminium (kg)
- Cuivre (kg)
- Plastique PVC (kg)
- Caoutchouc (kg)
- Verre trempé (m²)
- Bois de chêne (m³)
- Textile coton (m)

**Seuil critique :** < 20 unités

### **2. Smart Contracts** 📄

**Cycle de vie d'un contrat :**
```
DRAFT → PENDING_PRICE → READY_TO_EXECUTE → EXECUTED
```

**États expliqués :**
- **DRAFT** : Contrat créé mais pas encore initialisé
- **PENDING_PRICE** : En attente de confirmation du prix fournisseur
- **READY_TO_EXECUTE** : Prix confirmé, prêt pour déploiement
- **EXECUTED** : Contrat déployé sur la blockchain

**Contenu d'un contrat :**
- ID unique (ex: 483001B61ED46C00)
- Nom de la matière première
- Quantité à commander (toujours 40 unités = 2x le seuil)
- Fournisseur assigné
- Prix unitaire (une fois confirmé)

### **3. Communication Email** 📧

**Processus automatique :**
1. **Détection** d'un seuil critique
2. **Génération** d'un email HTML professionnel
3. **Envoi** au fournisseur (simulé)
4. **Attente** de réponse (24h maximum)
5. **Traitement** de la réponse pour extraire le prix

**Format de l'email :**
- En-tête professionnel
- Tableau avec détails de la commande
- Instructions pour la réponse
- Référence du contrat

### **4. Blockchain Hedera** 🔗

**Processus de déploiement :**
1. **Préparation** des données du contrat
2. **Génération** d'un hash de transaction unique
3. **Déploiement** sur le réseau Hedera (simulé)
4. **Vérification** du déploiement
5. **Enregistrement** dans l'historique

**Informations stockées :**
- Hash de transaction
- Données complètes du contrat
- Timestamp de déploiement
- Frais de réseau (simulés)

### **5. Flux Agentique** 🤖

**Intelligence artificielle qui :**
- **Analyse** les réponses des fournisseurs
- **Extrait** automatiquement les prix
- **Prend des décisions** sur l'exécution des contrats
- **Orchestre** tout le processus sans intervention humaine
- **Gère les erreurs** et les situations exceptionnelles

---

## 📊 INTERFACE ET AFFICHAGE

### **Écran Principal**
```
============================================================
           STATUT DU SYSTÈME
============================================================
Cycles exécutés: 7
Événements critiques: 39
Contrats créés: 39
Contrats exécutés: 0
Emails envoyés: 0

Matières surveillées: 8
Matières critiques: 8
Pourcentage critique: 100.0%

Contrats actifs: 39
Valeur totale: 0.00 €

Compte Hedera: 0.0.123456
Solde: 1000.000 HBAR
Déploiements: 0
============================================================
```

### **Messages Types**

**✅ Succès :**
```
✅ Tag RFID initialisé: Acier inoxydable - 32 kg
✅ Nouveau contrat créé - ID: 483001B61ED46C00
✅ Contrat déployé sur Hedera: 1a2b3c4d...
```

**⚠️ Alertes :**
```
⚠️ SEUIL CRITIQUE ATTEINT - Caoutchouc: 8 kg (seuil: 20)
⚠️ 3 matière(s) en situation critique détectée(s)
```

**❌ Erreurs :**
```
❌ Erreur lors de l'envoi de l'email: Username and Password not accepted
❌ Échec envoi email pour Caoutchouc
```

---

## 📁 FICHIERS GÉNÉRÉS

### **Logs Automatiques**
L'application génère automatiquement des fichiers de logs :

```
logs/
├── stock_management.log              # Log principal
├── rfid_export_YYYYMMDD_HHMMSS.json  # Données RFID
├── contracts_export_YYYYMMDD_HHMMSS.json # Contrats
├── email_logs_YYYYMMDD_HHMMSS.json   # Communications
├── hedera_deployments_YYYYMMDD_HHMMSS.json # Blockchain
└── complete_report_YYYYMMDD_HHMMSS.json # Rapport complet
```

### **Contenu des Logs**

**Exemple de log RFID :**
```json
{
  "tag_id": "RFID_123456",
  "material_name": "Acier inoxydable",
  "current_quantity": 15,
  "unit": "kg",
  "is_critical": true,
  "last_updated": "2025-07-15T15:54:11.845"
}
```

**Exemple de contrat :**
```json
{
  "contract_id": "483001B61ED46C00",
  "material_name": "Caoutchouc",
  "quantity_to_order": 40,
  "unit": "kg",
  "supplier": "RubberCorp",
  "status": "pending_price",
  "total_amount": 0.0
}
```

---

## ⚙️ CONFIGURATION

### **Variables Importantes**
Dans le fichier `.env` :

```bash
# Seuil critique (unités)
STOCK_THRESHOLD=20

# Intervalle de simulation (secondes)
RFID_SIMULATION_INTERVAL=5

# Quantités initiales (min-max)
INITIAL_STOCK_MIN=10
INITIAL_STOCK_MAX=50
```

### **Personnalisation**

**Changer le seuil critique :**
Modifiez `STOCK_THRESHOLD=20` dans `.env`

**Accélérer la simulation :**
Modifiez `RFID_SIMULATION_INTERVAL=5` (en secondes)

**Ajouter des matières :**
Modifiez la liste `MATERIALS` dans `config/settings.py`

---

## 🔧 RÉSOLUTION DE PROBLÈMES

### **Problème : "Module non trouvé"**
**Solution :**
```bash
pip install python-dotenv
```

### **Problème : "Permission refusée"**
**Solution :**
```bash
py -3 test_simple.py
# au lieu de
python test_simple.py
```

### **Problème : "Erreur SMTP"**
**Explication :** Normal ! L'application utilise des credentials de test.
**En production :** Configurez de vrais credentials SMTP dans `.env`

### **Problème : Application lente**
**Solution :** Réduisez `RFID_SIMULATION_INTERVAL` dans `.env`

---

## 📚 POUR ALLER PLUS LOIN

### **Comprendre les Logs**
Consultez les fichiers dans `logs/` pour voir :
- L'évolution des stocks en temps réel
- Les contrats générés automatiquement
- Les tentatives de communication
- L'historique des déploiements blockchain

### **Modifier le Comportement**
- Éditez `config/settings.py` pour changer les matières
- Modifiez `.env` pour ajuster les seuils
- Consultez `src/` pour comprendre le code

### **Étendre l'Application**
- Ajoutez de nouveaux types de capteurs
- Intégrez de vrais lecteurs RFID
- Connectez à un vrai réseau Hedera
- Développez une interface web

---

## 🎯 CONSEILS D'UTILISATION

### **Pour une Démonstration**
1. Lancez `py -3 demo.py`
2. Suivez les instructions à l'écran
3. Montrez les logs générés
4. Expliquez le flux agentique

### **Pour Comprendre le Code**
1. Commencez par `src/main.py`
2. Étudiez `src/rfid_simulator.py`
3. Analysez `src/smart_contract.py`
4. Explorez les autres modules

### **Pour une Présentation**
1. Préparez `py -3 test_simple.py` (validation)
2. Utilisez `py -3 demo.py` (démonstration)
3. Montrez `py -3 open_report.py` (rapport)
4. Expliquez l'innovation technique

---

## 🎨 DIAGRAMMES DE FONCTIONNEMENT

### **Flux Principal de l'Application**
```
┌─────────────────┐
│   DÉMARRAGE     │
│   Application   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ INITIALISATION  │
│ • 8 Tags RFID   │
│ • Configuration │
│ • Connexions    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐    ⚠️ Seuil < 20 ?
│ SURVEILLANCE    │────────────────┐
│ RFID Continue   │                │
│ (toutes les 5s) │                │
└─────────┬───────┘                │
          │                        │
          │ ✅ Stock OK             │ ❌ Stock Critique
          │                        │
          ▼                        ▼
┌─────────────────┐    ┌─────────────────┐
│   ATTENDRE      │    │ CRÉER CONTRAT   │
│   Prochain      │    │ • ID unique     │
│   Cycle         │    │ • Quantité: 40  │
└─────────────────┘    └─────────┬───────┘
                                 │
                                 ▼
                       ┌─────────────────┐
                       │ ENVOYER EMAIL   │
                       │ • HTML formaté  │
                       │ • Demande prix  │
                       └─────────┬───────┘
                                 │
                                 ▼
                       ┌─────────────────┐
                       │ TRAITER RÉPONSE │
                       │ • Extraire prix │
                       │ • Valider       │
                       └─────────┬───────┘
                                 │
                                 ▼
                       ┌─────────────────┐
                       │ DÉPLOYER        │
                       │ BLOCKCHAIN      │
                       │ • Hedera Hash   │
                       │ • Vérification  │
                       └─────────────────┘
```

### **États d'un Smart Contract**
```
📄 DRAFT
   │
   │ set_provisional_price()
   ▼
⏳ PENDING_PRICE
   │
   │ confirm_price()
   ▼
✅ READY_TO_EXECUTE
   │
   │ execute_contract()
   ▼
🔗 EXECUTED
```

### **Architecture des Modules**
```
┌─────────────────────────────────────────────────────────┐
│                    MAIN.PY                              │
│              (Orchestration Principale)                 │
└─────────────────────┬───────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│RFID         │ │SMART        │ │EMAIL        │
│SIMULATOR    │ │CONTRACT     │ │HANDLER      │
│             │ │MANAGER      │ │             │
│• Tags       │ │• États      │ │• SMTP       │
│• Seuils     │ │• Validation │ │• Parsing    │
│• Logs       │ │• Calculs    │ │• Simulation │
└─────────────┘ └─────────────┘ └─────────────┘
        │             │             │
        └─────────────┼─────────────┘
                      │
                      ▼
        ┌─────────────────────────────┐
        │     HASHGRAPH CLIENT        │
        │                             │
        │ • Connexion Hedera          │
        │ • Déploiement Contrats      │
        │ • Vérification              │
        │ • Flux Agentique            │
        └─────────────────────────────┘
```

---

## 🎯 SCÉNARIOS D'UTILISATION

### **Scénario 1 : Première Découverte**
```bash
# 1. Test rapide pour vérifier
py -3 test_simple.py

# 2. Démonstration guidée
py -3 demo.py

# 3. Consultation du rapport
py -3 open_report.py
```

### **Scénario 2 : Présentation Professionnelle**
```bash
# 1. Validation technique
py -3 test_simple.py

# 2. Démonstration en direct
py -3 run_system.py

# 3. Analyse des résultats
# Consulter les fichiers dans logs/
```

### **Scénario 3 : Développement/Debug**
```bash
# 1. Modifier la configuration
# Éditer .env ou config/settings.py

# 2. Test des modifications
py -3 test_simple.py

# 3. Validation complète
py -3 run_system.py
```

---

## 📱 INTERFACE UTILISATEUR

### **Messages de Statut**
L'application affiche en temps réel :

**🟢 Informations :**
- Initialisation des composants
- Cycles de surveillance
- Création de contrats

**🟡 Alertes :**
- Seuils critiques détectés
- Matières en rupture
- Timeouts de communication

**🔴 Erreurs :**
- Problèmes de connexion
- Échecs d'envoi email
- Erreurs de validation

### **Tableaux de Bord**
```
============================================================
           STATUT DU SYSTÈME
============================================================
Cycles exécutés: 7          ← Nombre de vérifications
Événements critiques: 39    ← Alertes déclenchées
Contrats créés: 39          ← Smart contracts générés
Contrats exécutés: 0        ← Déploiements blockchain
Emails envoyés: 0           ← Communications fournisseurs

Matières surveillées: 8     ← Types de matières premières
Matières critiques: 8       ← En situation de rupture
Pourcentage critique: 100%  ← Pourcentage d'alerte

Contrats actifs: 39         ← Contrats en cours
Valeur totale: 0.00 €      ← Montant des commandes

Compte Hedera: 0.0.123456   ← ID compte blockchain
Solde: 1000.000 HBAR       ← Solde disponible
Déploiements: 0             ← Transactions blockchain
============================================================
```

---

**🎉 Vous maîtrisez maintenant le fonctionnement complet de l'application !**

*Pour toute question, consultez la documentation technique ou les commentaires dans le code source.*
