#!/usr/bin/env python3
"""
Test simple du système sans dépendances externes
"""
import sys
import os

# Ajout du répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test des imports des modules"""
    try:
        print("Test des imports...")
        
        # Test import config (sans dotenv)
        print("  - Configuration... ", end="")
        # Simuler les variables d'environnement
        os.environ['HEDERA_NETWORK'] = 'testnet'
        os.environ['HEDERA_ACCOUNT_ID'] = '0.0.123456'
        os.environ['HEDERA_PRIVATE_KEY'] = 'test_key'
        os.environ['EMAIL_ADDRESS'] = '<EMAIL>'
        os.environ['EMAIL_PASSWORD'] = 'test_pass'
        os.environ['SUPPLIER_EMAIL'] = '<EMAIL>'
        
        from config.settings import Config, MATERIALS
        print("✅")
        
        print("  - RFID Simulator... ", end="")
        from rfid_simulator import RFIDSimulator, RFIDTag
        print("✅")
        
        print("  - Smart Contract... ", end="")
        from smart_contract import SmartContractManager, SmartContract
        print("✅")
        
        print("  - Email Handler... ", end="")
        from email_handler import EmailHandler
        print("✅")
        
        print("  - Hashgraph Client... ", end="")
        from hashgraph_client import HashgraphClient, AgenticWorkflow
        print("✅")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        return False

def test_basic_functionality():
    """Test des fonctionnalités de base"""
    try:
        print("\nTest des fonctionnalités de base...")
        
        # Test RFID
        print("  - RFID Simulator... ", end="")
        from rfid_simulator import RFIDSimulator
        simulator = RFIDSimulator()
        assert len(simulator.tags) > 0
        print("✅")
        
        # Test Smart Contract
        print("  - Smart Contract... ", end="")
        from smart_contract import SmartContractManager
        manager = SmartContractManager()
        contract = manager.create_contract("Test", 50, "kg", "TestSupplier", 10.0)
        assert contract.material_name == "Test"
        print("✅")
        
        # Test Email Handler
        print("  - Email Handler... ", end="")
        from email_handler import EmailHandler
        email_handler = EmailHandler()
        # Simulation d'envoi
        email_handler.sent_requests["TEST"] = {
            "contract_id": "TEST",
            "material_name": "Test",
            "quantity": 50,
            "unit": "kg",
            "supplier": "TestSupplier",
            "sent_at": "2024-01-01T00:00:00",
            "status": "sent"
        }
        response = email_handler.simulate_supplier_response("TEST", 15.0)
        assert response["contract_id"] == "TEST"
        print("✅")
        
        # Test Hashgraph Client
        print("  - Hashgraph Client... ", end="")
        from hashgraph_client import HashgraphClient
        client = HashgraphClient()
        assert client.client is not None
        print("✅")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        return False

def test_workflow():
    """Test du workflow complet"""
    try:
        print("\nTest du workflow complet...")
        
        from rfid_simulator import RFIDSimulator
        from smart_contract import SmartContractManager
        from email_handler import EmailHandler
        from hashgraph_client import AgenticWorkflow
        
        # Initialisation
        simulator = RFIDSimulator()
        contract_manager = SmartContractManager()
        email_handler = EmailHandler()
        workflow = AgenticWorkflow()
        
        print("  - Composants initialisés ✅")
        
        # Simulation d'un matériau critique
        critical_material = simulator.tags[0]
        critical_material.update_quantity(15)  # En dessous du seuil
        
        # Création d'un contrat
        contract = contract_manager.create_contract(
            material_name=critical_material.material_name,
            quantity_to_order=40,
            unit=critical_material.unit,
            supplier=critical_material.supplier,
            provisional_price=0.0
        )
        
        print("  - Contrat créé ✅")
        
        # Simulation d'email
        email_handler.sent_requests[contract.contract_id] = {
            "contract_id": contract.contract_id,
            "material_name": contract.material_name,
            "quantity": contract.quantity_to_order,
            "unit": contract.unit,
            "supplier": contract.supplier,
            "sent_at": "2024-01-01T00:00:00",
            "status": "sent"
        }

        # Mettre le contrat en état PENDING_PRICE
        contract.set_provisional_price(0.0)

        response = email_handler.simulate_supplier_response(contract.contract_id, 20.0)
        print("  - Réponse email simulée ✅")

        # Workflow agentique
        hedera_hash = workflow.process_price_confirmation(contract, response)
        print(f"  - Déploiement Hashgraph: {hedera_hash[:16]}... ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    print("="*60)
    print("  TEST SIMPLE DU SYSTÈME DE GESTION DES STOCKS")
    print("="*60)
    
    success = True
    
    # Test des imports
    if not test_imports():
        success = False
    
    # Test des fonctionnalités
    if not test_basic_functionality():
        success = False
    
    # Test du workflow
    if not test_workflow():
        success = False
    
    print("\n" + "="*60)
    if success:
        print("  🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!")
        print("  Le système est prêt pour la démonstration.")
    else:
        print("  ❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("  Vérifiez les erreurs ci-dessus.")
    print("="*60)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
