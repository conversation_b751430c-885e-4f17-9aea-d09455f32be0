#!/usr/bin/env python3
"""
Script pour ouvrir le rapport généré dans le navigateur
"""
import os
import webbrowser
import glob
from datetime import datetime

def find_latest_report():
    """Trouve le rapport le plus récent"""
    # Chercher tous les fichiers de rapport
    html_files = glob.glob("Rapport_Projet_*.html")
    pdf_files = glob.glob("Rapport_Projet_*.pdf")
    
    all_files = html_files + pdf_files
    
    if not all_files:
        return None
    
    # Trier par date de modification (le plus récent en premier)
    all_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    return all_files[0]

def open_report_in_browser(file_path):
    """Ouvre le rapport dans le navigateur par défaut"""
    try:
        # Obtenir le chemin absolu
        abs_path = os.path.abspath(file_path)
        
        # Ouvrir dans le navigateur
        webbrowser.open(f'file://{abs_path}')
        
        print(f"✅ Rapport ouvert dans le navigateur : {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'ouverture : {str(e)}")
        return False

def list_available_reports():
    """Liste tous les rapports disponibles"""
    html_files = glob.glob("Rapport_Projet_*.html")
    pdf_files = glob.glob("Rapport_Projet_*.pdf")
    
    all_files = html_files + pdf_files
    
    if not all_files:
        print("❌ Aucun rapport trouvé")
        return []
    
    print("📋 Rapports disponibles :")
    for i, file in enumerate(all_files, 1):
        file_size = os.path.getsize(file)
        file_time = datetime.fromtimestamp(os.path.getmtime(file))
        file_type = "PDF" if file.endswith('.pdf') else "HTML"
        
        print(f"  {i}. 📄 {file}")
        print(f"     Type: {file_type} | Taille: {file_size:,} bytes")
        print(f"     Modifié: {file_time.strftime('%d/%m/%Y %H:%M:%S')}")
        print()
    
    return all_files

def main():
    """Fonction principale"""
    print("="*60)
    print("  OUVERTURE DU RAPPORT DE PROJET")
    print("  Suivi Intelligent des Stocks avec RFID et Hashgraph")
    print("="*60)
    
    # Lister les rapports disponibles
    available_reports = list_available_reports()
    
    if not available_reports:
        print("\n💡 Générez d'abord un rapport avec :")
        print("   py -3 generate_pdf_report.py")
        return
    
    # Trouver le rapport le plus récent
    latest_report = find_latest_report()
    
    if latest_report:
        print(f"🎯 Ouverture du rapport le plus récent : {latest_report}")
        
        if open_report_in_browser(latest_report):
            print("\n🎉 Le rapport s'ouvre dans votre navigateur par défaut")
            print("📖 Vous pouvez maintenant consulter le rapport complet")
            
            if latest_report.endswith('.html'):
                print("\n💡 Conseils pour le rapport HTML :")
                print("   - Utilisez Ctrl+P pour imprimer ou sauvegarder en PDF")
                print("   - Le rapport est optimisé pour l'impression")
                print("   - Tous les styles sont intégrés")
        else:
            print(f"\n❌ Impossible d'ouvrir le rapport automatiquement")
            print(f"📁 Ouvrez manuellement le fichier : {latest_report}")
    
    print(f"\n📂 Emplacement des fichiers :")
    print(f"   {os.path.abspath('.')}")

if __name__ == "__main__":
    main()
