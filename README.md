# Suivi Intelligent des Stocks avec RFID et Hashgraph

## Description du Projet

Ce projet implémente une solution IoT sécurisée basée sur la technologie Hashgraph et des smart contracts dynamiques, permettant de simuler la gestion automatisée des stocks de matières premières à l'aide de tags RFID.

## Fonctionnalités

1. **Simulation d'un lecteur RFID** - Génération dynamique d'un stock simulé de matières premières
2. **Détection de seuils critiques** - Surveillance automatique des niveaux de stock (< 20 unités)
3. **Génération de smart contracts** - Création automatique de contrats avec prix provisoire
4. **Communication automatisée** - Envoi d'emails aux fournisseurs pour demandes de prix
5. **Déploiement sur Hashgraph** - Finalisation et déploiement des commandes

## Structure du Projet

```
blockchain_project/
├── src/
│   ├── rfid_simulator.py      # Simulation du lecteur RFID
│   ├── smart_contract.py      # Gestion des smart contracts
│   ├── email_handler.py       # Gestion des emails
│   ├── hashgraph_client.py    # Client Hedera Hashgraph
│   └── main.py               # Point d'entrée principal
├── config/
│   └── settings.py           # Configuration centralisée
├── logs/                     # Fichiers de logs
├── tests/                    # Tests unitaires
├── requirements.txt          # Dépendances Python
├── .env.example             # Variables d'environnement exemple
└── README.md               # Documentation

```

## Installation

1. Cloner le projet
2. Copier `.env.example` vers `.env` et configurer les variables
3. Installer les dépendances : `pip install -r requirements.txt`
4. Exécuter : `python src/main.py`

## Configuration

Configurer les variables d'environnement dans le fichier `.env` :
- Credentials Hedera Hashgraph
- Configuration SMTP pour les emails
- Paramètres de simulation RFID

## Auteur

Pr Omar SOUISSI
