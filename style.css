/* Style CSS pour le rapport de projet */

/* Variables CSS */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-gray: #f8f9fa;
    --medium-gray: #6c757d;
    --dark-gray: #343a40;
    --border-color: #dee2e6;
}

/* Base */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: #f5f7fa;
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 0 30px rgba(0,0,0,0.1);
}

/* Typographie */
h1 {
    color: var(--primary-color);
    font-size: 2.5em;
    margin-bottom: 0.5em;
    border-bottom: 4px solid var(--secondary-color);
    padding-bottom: 15px;
    text-align: center;
}

h2 {
    color: var(--primary-color);
    font-size: 1.8em;
    margin-top: 2em;
    margin-bottom: 1em;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
}

h3 {
    color: var(--secondary-color);
    font-size: 1.4em;
    margin-top: 1.5em;
    margin-bottom: 0.8em;
}

h4 {
    color: var(--medium-gray);
    font-size: 1.2em;
    margin-top: 1.2em;
    margin-bottom: 0.6em;
}

p {
    margin-bottom: 1em;
    text-align: justify;
}

/* Listes */
ul, ol {
    margin-bottom: 1em;
    padding-left: 2em;
}

li {
    margin-bottom: 0.5em;
}

/* Code et préformaté */
code {
    background-color: var(--light-gray);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', Consolas, monospace;
    font-size: 0.9em;
    color: var(--accent-color);
}

pre {
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    border-left: 4px solid var(--secondary-color);
    margin: 1.5em 0;
}

pre code {
    background-color: transparent;
    padding: 0;
    color: var(--dark-gray);
}

/* Tableaux */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 2em 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background-color: var(--secondary-color);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9em;
    letter-spacing: 0.5px;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e3f2fd;
}

/* Blocs spéciaux */
.highlight {
    background-color: #fff3cd;
    border-left: 4px solid var(--warning-color);
    padding: 15px;
    margin: 1.5em 0;
    border-radius: 0 4px 4px 0;
}

.success {
    background-color: #d4edda;
    border-left: 4px solid var(--success-color);
    padding: 15px;
    margin: 1.5em 0;
    border-radius: 0 4px 4px 0;
}

.info {
    background-color: #d1ecf1;
    border-left: 4px solid var(--info-color);
    padding: 15px;
    margin: 1.5em 0;
    border-radius: 0 4px 4px 0;
}

.error {
    background-color: #f8d7da;
    border-left: 4px solid var(--accent-color);
    padding: 15px;
    margin: 1.5em 0;
    border-radius: 0 4px 4px 0;
}

/* Liens */
a {
    color: var(--secondary-color);
    text-decoration: none;
    border-bottom: 1px dotted var(--secondary-color);
    transition: all 0.3s ease;
}

a:hover {
    color: var(--primary-color);
    border-bottom: 1px solid var(--primary-color);
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin: 1em 0;
}

/* Blockquotes */
blockquote {
    border-left: 4px solid var(--secondary-color);
    padding-left: 20px;
    margin: 1.5em 0;
    font-style: italic;
    color: var(--medium-gray);
    background-color: var(--light-gray);
    padding: 15px 20px;
    border-radius: 0 4px 4px 0;
}

/* Table des matières */
.toc {
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: 8px;
    margin: 2em 0;
    border: 1px solid var(--border-color);
}

.toc h2 {
    margin-top: 0;
    color: var(--primary-color);
    border-bottom: 2px solid var(--secondary-color);
}

.toc ul {
    list-style-type: none;
    padding-left: 0;
}

.toc li {
    margin-bottom: 0.3em;
}

.toc a {
    border-bottom: none;
    padding: 5px 10px;
    display: block;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.toc a:hover {
    background-color: white;
}

/* Footer */
.footer {
    margin-top: 3em;
    padding-top: 2em;
    border-top: 2px solid var(--border-color);
    text-align: center;
    color: var(--medium-gray);
    font-size: 0.9em;
}

/* Badges et étiquettes */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.8em;
    font-weight: bold;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background-color: var(--success-color);
    color: white;
}

.badge-warning {
    background-color: var(--warning-color);
    color: white;
}

.badge-info {
    background-color: var(--info-color);
    color: white;
}

.badge-error {
    background-color: var(--accent-color);
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 20px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    h2 {
        font-size: 1.5em;
    }
    
    table {
        font-size: 0.9em;
    }
    
    th, td {
        padding: 8px 10px;
    }
}

/* Impression */
@media print {
    body {
        background-color: white;
        padding: 0;
    }
    
    .container {
        box-shadow: none;
        padding: 20px;
    }
    
    a {
        color: black;
        border-bottom: none;
    }
    
    .highlight, .success, .info, .error {
        border: 1px solid #ccc;
        background-color: #f9f9f9 !important;
    }
    
    pre {
        border: 1px solid #ccc;
        background-color: #f9f9f9 !important;
    }
}
