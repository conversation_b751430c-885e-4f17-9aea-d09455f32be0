#!/usr/bin/env python3
"""
Script de démonstration du système de suivi intelligent des stocks
Blockchain et IoT - RFID et Hashgraph
"""
import os
import sys
import time
from datetime import datetime

# Ajout du répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from main import StockManagementSystem

def print_banner():
    """Affiche la bannière du projet"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║           SUIVI INTELLIGENT DES STOCKS AVEC RFID ET HASHGRAPH               ║
║                                                                              ║
║                          Mini-projet Blockchain et IoT                      ║
║                                Pr Omar SOUISSI                              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_section(title):
    """Affiche un titre de section"""
    print(f"\n{'='*80}")
    print(f"  {title}")
    print(f"{'='*80}")

def wait_for_user(message="Appuyez sur Entrée pour continuer..."):
    """Attend une action de l'utilisateur"""
    input(f"\n{message}")

def demo_step_1():
    """Étape 1: Présentation du système"""
    print_section("ÉTAPE 1: PRÉSENTATION DU SYSTÈME")
    
    print("""
Ce système démontre l'intégration de plusieurs technologies avancées :

🏷️  RFID Simulation    : Surveillance en temps réel des stocks de matières premières
📄  Smart Contracts   : Génération automatique de contrats avec gestion d'états
📧  Communication     : Envoi automatisé d'emails aux fournisseurs
🔗  Blockchain        : Déploiement sur Hedera Hashgraph pour la traçabilité
🤖  Flux Agentique    : Orchestration intelligente de tout le processus

Le système surveille 8 matières premières différentes et déclenche automatiquement
des commandes lorsque le stock passe en dessous du seuil critique de 20 unités.
    """)
    
    wait_for_user()

def demo_step_2():
    """Étape 2: Initialisation du système"""
    print_section("ÉTAPE 2: INITIALISATION DU SYSTÈME")
    
    print("Initialisation des composants du système...")
    
    try:
        system = StockManagementSystem()
        system.initialize_system()
        
        print("\n✅ Système initialisé avec succès!")
        print(f"   - Tags RFID créés: {len(system.rfid_simulator.tags)}")
        print(f"   - Seuil critique: {system.rfid_simulator.logger.handlers[0].formatter._fmt}")
        print(f"   - Réseau Hedera: Testnet (simulé)")
        
        return system
        
    except Exception as e:
        print(f"\n❌ Erreur lors de l'initialisation: {str(e)}")
        return None

def demo_step_3(system):
    """Étape 3: Démonstration de la surveillance RFID"""
    print_section("ÉTAPE 3: SURVEILLANCE RFID EN TEMPS RÉEL")
    
    print("Affichage de l'état initial des stocks...")
    
    # Affichage des stocks initiaux
    stock_status = system.rfid_simulator.get_stock_status()
    print(f"\nMatières premières surveillées: {stock_status['total_materials']}")
    
    print("\nDétail des stocks:")
    for material in stock_status['materials'][:5]:  # Afficher les 5 premiers
        status = "🔴 CRITIQUE" if material['is_critical'] else "🟢 OK"
        print(f"  - {material['material_name']}: {material['current_quantity']} {material['unit']} {status}")
    
    print("\nSimulation de consommation de stock...")
    wait_for_user("Appuyez sur Entrée pour démarrer la simulation...")
    
    # Simulation de quelques cycles
    for i in range(3):
        print(f"\n--- Cycle {i+1} ---")
        critical_events = system.rfid_simulator.simulate_stock_consumption()
        
        if critical_events:
            print(f"⚠️  {len(critical_events)} événement(s) critique(s) détecté(s)!")
            for event in critical_events:
                material = event['tag_data']
                print(f"   - {material['material_name']}: {material['current_quantity']} {material['unit']}")
        else:
            print("✅ Aucun événement critique détecté")
        
        time.sleep(2)

def demo_step_4(system):
    """Étape 4: Génération de smart contracts"""
    print_section("ÉTAPE 4: GÉNÉRATION DE SMART CONTRACTS")
    
    print("Recherche de matières premières en situation critique...")
    
    critical_materials = system.rfid_simulator.get_critical_materials()
    
    if not critical_materials:
        print("Aucune matière critique trouvée. Forçage d'une situation critique...")
        # Forcer une situation critique
        test_material = system.rfid_simulator.tags[0]
        test_material.update_quantity(15)  # En dessous du seuil
        critical_materials = [test_material]
    
    print(f"\n{len(critical_materials)} matière(s) critique(s) trouvée(s):")
    
    for material in critical_materials:
        print(f"  - {material.material_name}: {material.current_quantity} {material.unit}")
        
        # Création du smart contract
        quantity_to_order = 40  # 2x le seuil
        contract = system.contract_manager.create_contract(
            material_name=material.material_name,
            quantity_to_order=quantity_to_order,
            unit=material.unit,
            supplier=material.supplier,
            provisional_price=0.0
        )
        
        print(f"    ✅ Smart contract créé: {contract.contract_id}")
        print(f"    📦 Quantité à commander: {quantity_to_order} {material.unit}")
        print(f"    🏢 Fournisseur: {material.supplier}")
        print(f"    📊 Statut: {contract.status.value}")
    
    wait_for_user()
    return critical_materials

def demo_step_5(system, critical_materials):
    """Étape 5: Communication avec les fournisseurs"""
    print_section("ÉTAPE 5: COMMUNICATION AUTOMATISÉE")
    
    print("Envoi d'emails automatiques aux fournisseurs...")
    
    for material in critical_materials:
        print(f"\n📧 Envoi de demande de prix pour: {material.material_name}")
        
        # Simulation d'envoi d'email
        contract_id = f"AUTO_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Ajout direct à la liste des demandes (simulation)
        system.email_handler.sent_requests[contract_id] = {
            "contract_id": contract_id,
            "material_name": material.material_name,
            "quantity": 40,
            "unit": material.unit,
            "supplier": material.supplier,
            "sent_at": datetime.now().isoformat(),
            "status": "sent"
        }
        
        print(f"   ✅ Email envoyé au fournisseur: {material.supplier}")
        print(f"   📋 Référence contrat: {contract_id}")
        
        # Simulation de réponse
        print(f"   ⏳ Attente de réponse du fournisseur...")
        time.sleep(2)
        
        response = system.email_handler.simulate_supplier_response(contract_id, base_price=20.0)
        print(f"   📨 Réponse reçue! Prix: {response['unit_price']:.2f} €/{material.unit}")
        print(f"   ⏰ Délai de livraison: {response['delivery_time']}")
    
    wait_for_user()

def demo_step_6(system):
    """Étape 6: Déploiement sur Hashgraph"""
    print_section("ÉTAPE 6: DÉPLOIEMENT SUR HEDERA HASHGRAPH")
    
    print("Finalisation et déploiement des contrats sur la blockchain...")
    
    # Récupération des contrats en attente
    pending_contracts = system.contract_manager.get_pending_contracts()
    
    if not pending_contracts:
        print("Aucun contrat en attente. Création d'un contrat de test...")
        # Créer un contrat de test
        test_contract = system.contract_manager.create_contract(
            material_name="Acier inoxydable",
            quantity_to_order=50,
            unit="kg",
            supplier="MetalCorp",
            provisional_price=15.50
        )
        test_contract.set_provisional_price(15.50)
        test_contract.confirm_price(16.20)
        pending_contracts = [test_contract]
    
    for contract in pending_contracts[:2]:  # Limiter à 2 contrats pour la démo
        print(f"\n🔗 Traitement du contrat: {contract.contract_id}")
        print(f"   📦 Matière: {contract.material_name}")
        print(f"   💰 Prix confirmé: {contract.confirmed_price or 'En attente'} €/{contract.unit}")
        
        if contract.status.value == "pending_price":
            # Simulation de confirmation de prix
            contract.confirm_price(16.20)
            print(f"   ✅ Prix confirmé: 16.20 €/{contract.unit}")
        
        if contract.status.value == "ready_to_execute":
            try:
                # Exécution locale
                execution_hash = contract.execute_contract()
                print(f"   🔧 Contrat exécuté localement: {execution_hash[:16]}...")
                
                # Déploiement sur Hedera
                contract_data = {
                    "contract_id": contract.contract_id,
                    "material_name": contract.material_name,
                    "quantity": contract.quantity_to_order,
                    "unit": contract.unit,
                    "supplier": contract.supplier,
                    "unit_price": contract.confirmed_price,
                    "total_amount": contract.get_total_amount()
                }
                
                hedera_hash = system.workflow.hashgraph_client.deploy_purchase_order(contract_data)
                print(f"   🌐 Déployé sur Hedera: {hedera_hash[:16]}...")
                print(f"   💵 Montant total: {contract.get_total_amount():.2f} €")
                
                # Vérification
                if system.workflow.hashgraph_client.verify_deployment(hedera_hash):
                    print(f"   ✅ Déploiement vérifié avec succès!")
                
            except Exception as e:
                print(f"   ❌ Erreur lors du déploiement: {str(e)}")
    
    wait_for_user()

def demo_step_7(system):
    """Étape 7: Rapport final et statistiques"""
    print_section("ÉTAPE 7: RAPPORT FINAL ET STATISTIQUES")
    
    print("Génération du rapport final du système...")
    
    # Statistiques du système
    print("\n📊 STATISTIQUES DU SYSTÈME:")
    print(f"   - Cycles de surveillance: {system.system_stats['total_cycles']}")
    print(f"   - Événements critiques: {system.system_stats['critical_events']}")
    print(f"   - Contrats créés: {system.system_stats['contracts_created']}")
    print(f"   - Contrats exécutés: {system.system_stats['contracts_executed']}")
    print(f"   - Emails envoyés: {system.system_stats['emails_sent']}")
    
    # Statut des stocks
    stock_status = system.rfid_simulator.get_stock_status()
    print(f"\n📦 STATUT DES STOCKS:")
    print(f"   - Matières surveillées: {stock_status['total_materials']}")
    print(f"   - Matières critiques: {stock_status['critical_materials']}")
    print(f"   - Pourcentage critique: {stock_status['critical_percentage']:.1f}%")
    
    # Informations Hedera
    account_info = system.workflow.hashgraph_client.get_account_info()
    print(f"\n🔗 COMPTE HEDERA HASHGRAPH:")
    print(f"   - Compte ID: {account_info.get('account_id', 'N/A')}")
    print(f"   - Réseau: {account_info.get('network', 'N/A')}")
    print(f"   - Solde: {account_info.get('balance', 0):.3f} HBAR")
    print(f"   - Déploiements: {account_info.get('total_deployments', 0)}")
    
    # Export du rapport
    print(f"\n📄 Génération du rapport complet...")
    report_file = system.export_complete_report()
    print(f"   ✅ Rapport exporté: {report_file}")
    
    print(f"\n🎉 DÉMONSTRATION TERMINÉE AVEC SUCCÈS!")
    print(f"   Tous les objectifs du mini-projet ont été atteints:")
    print(f"   ✅ Simulation RFID fonctionnelle")
    print(f"   ✅ Smart contracts dynamiques")
    print(f"   ✅ Communication automatisée")
    print(f"   ✅ Déploiement Hashgraph")
    print(f"   ✅ Flux agentique complet")

def main():
    """Fonction principale de démonstration"""
    print_banner()
    
    try:
        # Étape 1: Présentation
        demo_step_1()
        
        # Étape 2: Initialisation
        system = demo_step_2()
        if not system:
            print("❌ Impossible de continuer sans système initialisé")
            return 1
        
        # Étape 3: Surveillance RFID
        demo_step_3(system)
        
        # Étape 4: Smart contracts
        critical_materials = demo_step_4(system)
        
        # Étape 5: Communication
        demo_step_5(system, critical_materials)
        
        # Étape 6: Déploiement Hashgraph
        demo_step_6(system)
        
        # Étape 7: Rapport final
        demo_step_7(system)
        
        print(f"\n{'='*80}")
        print(f"  MERCI D'AVOIR SUIVI CETTE DÉMONSTRATION!")
        print(f"{'='*80}")
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️  Démonstration interrompue par l'utilisateur")
        return 1
    except Exception as e:
        print(f"\n\n❌ Erreur durant la démonstration: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main())
