#!/usr/bin/env python3
"""
Script d'aide pour l'application de suivi des stocks
"""
import os
import webbrowser
import subprocess
from datetime import datetime

def print_banner():
    """Affiche la bannière d'aide"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                           🆘 AIDE ET SUPPORT                                ║
║                                                                              ║
║           Suivi Intelligent des Stocks avec RFID et Hashgraph               ║
║                        <PERSON><PERSON><PERSON><PERSON>OUHAIR                           ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def show_quick_help():
    """Affiche l'aide rapide"""
    print("🚀 COMMANDES PRINCIPALES")
    print("="*50)
    print("1. py -3 test_simple.py      → Test rapide (30s)")
    print("2. py -3 demo.py             → Démonstration interactive (5-10min)")
    print("3. py -3 run_system.py       → Système complet (2min)")
    print("4. py -3 open_report.py      → Ouvrir le rapport technique")
    print("5. py -3 aide.py             → Cette aide")
    print()

def show_file_structure():
    """Affiche la structure des fichiers"""
    print("📁 STRUCTURE DU PROJET")
    print("="*50)
    
    structure = {
        "📖 Documentation": [
            "MODE_EMPLOI.md - Guide complet d'utilisation",
            "GUIDE_DEMARRAGE_RAPIDE.md - Démarrage en 5 minutes",
            "README.md - Documentation principale",
            "RAPPORT_PROJET_FINAL.md - Rapport académique"
        ],
        "🐍 Code Source": [
            "src/main.py - Point d'entrée principal",
            "src/rfid_simulator.py - Simulation RFID",
            "src/smart_contract.py - Smart contracts",
            "src/email_handler.py - Communication email",
            "src/hashgraph_client.py - Client blockchain"
        ],
        "🎮 Scripts d'Exécution": [
            "demo.py - Démonstration interactive",
            "test_simple.py - Tests de validation",
            "run_system.py - Système automatique",
            "open_report.py - Ouverture du rapport"
        ],
        "⚙️ Configuration": [
            ".env - Variables d'environnement",
            "config/settings.py - Configuration centralisée",
            "requirements.txt - Dépendances Python"
        ]
    }
    
    for category, files in structure.items():
        print(f"\n{category}:")
        for file in files:
            print(f"  • {file}")

def open_documentation():
    """Ouvre la documentation dans le navigateur"""
    docs = [
        ("MODE_EMPLOI.md", "Mode d'emploi complet"),
        ("GUIDE_DEMARRAGE_RAPIDE.md", "Guide de démarrage rapide"),
        ("README.md", "Documentation principale")
    ]
    
    print("\n📖 DOCUMENTATION DISPONIBLE")
    print("="*50)
    
    for i, (file, desc) in enumerate(docs, 1):
        if os.path.exists(file):
            print(f"{i}. {desc}")
        else:
            print(f"{i}. {desc} (fichier manquant)")
    
    print("0. Retour au menu principal")
    
    try:
        choice = input("\nChoisissez un document à ouvrir (0-3): ").strip()
        
        if choice == "0":
            return
        elif choice in ["1", "2", "3"]:
            file_path = docs[int(choice)-1][0]
            if os.path.exists(file_path):
                # Convertir en HTML temporaire pour l'affichage
                html_content = create_temp_html(file_path)
                temp_file = f"temp_doc_{datetime.now().strftime('%H%M%S')}.html"
                
                with open(temp_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                abs_path = os.path.abspath(temp_file)
                webbrowser.open(f'file://{abs_path}')
                print(f"✅ Document ouvert: {docs[int(choice)-1][1]}")
            else:
                print(f"❌ Fichier non trouvé: {file_path}")
        else:
            print("❌ Choix invalide")
            
    except (ValueError, IndexError):
        print("❌ Choix invalide")

def create_temp_html(md_file):
    """Crée un HTML temporaire à partir d'un fichier Markdown"""
    try:
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        html = f"""
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{md_file}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; border-bottom: 1px solid #bdc3c7; padding-bottom: 5px; }}
        h3 {{ color: #7f8c8d; }}
        code {{ background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }}
        pre {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }}
        .highlight {{ background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; }}
    </style>
</head>
<body>
    <div class="container">
        <pre>{content}</pre>
    </div>
</body>
</html>
        """
        return html
        
    except Exception as e:
        return f"<html><body><h1>Erreur</h1><p>Impossible de lire le fichier: {str(e)}</p></body></html>"

def run_quick_test():
    """Lance un test rapide"""
    print("\n🧪 LANCEMENT DU TEST RAPIDE")
    print("="*50)
    print("Exécution de: py -3 test_simple.py")
    print("Veuillez patienter...")
    
    try:
        result = subprocess.run(['py', '-3', 'test_simple.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Test réussi !")
            print("\nRésultat:")
            print(result.stdout)
        else:
            print("❌ Test échoué")
            print("Erreur:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ Test interrompu (timeout)")
    except FileNotFoundError:
        print("❌ Python non trouvé. Essayez 'python' au lieu de 'py -3'")
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")

def show_troubleshooting():
    """Affiche le guide de dépannage"""
    print("\n🔧 GUIDE DE DÉPANNAGE")
    print("="*50)
    
    problems = [
        {
            "problem": "❌ 'py' n'est pas reconnu",
            "solution": "Utilisez 'python' au lieu de 'py -3'"
        },
        {
            "problem": "❌ Module 'dotenv' non trouvé",
            "solution": "Exécutez: pip install python-dotenv"
        },
        {
            "problem": "❌ Erreurs SMTP dans les logs",
            "solution": "Normal ! L'app utilise des credentials de test"
        },
        {
            "problem": "❌ Application lente",
            "solution": "Normal, simulation réaliste (5s par cycle)"
        },
        {
            "problem": "❌ Fichiers de logs manquants",
            "solution": "Lancez d'abord: py -3 run_system.py"
        }
    ]
    
    for i, item in enumerate(problems, 1):
        print(f"\n{i}. {item['problem']}")
        print(f"   💡 {item['solution']}")

def main_menu():
    """Menu principal d'aide"""
    while True:
        print_banner()
        show_quick_help()
        
        print("📋 MENU D'AIDE")
        print("="*50)
        print("1. 📖 Ouvrir la documentation")
        print("2. 📁 Voir la structure du projet")
        print("3. 🧪 Lancer un test rapide")
        print("4. 🔧 Guide de dépannage")
        print("5. 📊 Ouvrir le rapport technique")
        print("0. Quitter")
        
        choice = input("\nVotre choix (0-5): ").strip()
        
        if choice == "0":
            print("\n👋 Au revoir !")
            break
        elif choice == "1":
            open_documentation()
        elif choice == "2":
            show_file_structure()
        elif choice == "3":
            run_quick_test()
        elif choice == "4":
            show_troubleshooting()
        elif choice == "5":
            try:
                subprocess.run(['py', '-3', 'open_report.py'])
            except:
                print("❌ Impossible d'ouvrir le rapport automatiquement")
                print("💡 Essayez: py -3 open_report.py")
        else:
            print("❌ Choix invalide")
        
        input("\nAppuyez sur Entrée pour continuer...")
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    main_menu()
